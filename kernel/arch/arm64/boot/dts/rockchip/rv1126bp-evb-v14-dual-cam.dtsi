// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

/*
 * csi2_dphy0 -> csi0(rx0) clk0 + 4 lane
 * csi2_dphy1 -> csi0(rx0) clk0 + 2 lane 0/1
 * csi2_dphy2 -> csi0(rx0) clk1 + 2 lane 2/3
 * csi2_dphy3 -> csi1(rx1) clk0 + 4 lane
 * csi2_dphy4 -> csi1(rx1) clk0 + 2 lane 0/1
 * csi2_dphy5 -> csi1(rx1) clk1 + 2 lane 2/3
 */

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			// csi_dphy_input0: endpoint@1 {
			// 	reg = <1>;
			// 	remote-endpoint = <&sc450ai_out>;
			// 	data-lanes = <1 2 3 4>;
			// };
			
			mipi_in_ucam0_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};

			mipi_in_ucam0_2815: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&ucam_out0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;


#if 1
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
			};
#else
			csi_dphy1_output: endpoint@0 {
				reg = <0>;
				// remote-endpoint = <&isp_in>;
			};
#endif
			// csidphy0_out: endpoint@0 {
			// 	reg = <0>;
			// 	remote-endpoint = <&mipi0_csi2_input>;
			// };
		};
	};
};

&csi2_dphy3 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			// csi_dphy3_input0: endpoint@1 {
			// 	reg = <1>;
			// 	remote-endpoint = <&sc450ai_1_out>;
			// 	data-lanes = <1 2 3 4>;
			// };
			csi_dphy1_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out1>;
				data-lanes = <1 2>;
			};

			
			csi_dphy1_input_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			
#if 1
			csi_dphy1_output: endpoint@0 {
				reg = <0>;
				// remote-endpoint = <&isp_in>;
				data-lanes = <1 2>;
			};
#else
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
			};
#endif
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;

	// sc450ai: sc450ai@30 {
	// 	compatible = "smartsens,sc450ai";
	// 	status = "okay";
	// 	reg = <0x30>;
	// 	clocks = <&cru CLK_MIPI0_OUT2IO>;
	// 	clock-names = "xvclk";
	// 	reset-gpios = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
	// 	pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
	// 	pinctrl-names = "default";
	// 	pinctrl-0 = <&cam_clk0_pins>;
	// 	rockchip,camera-module-index = <0>;
	// 	rockchip,camera-module-facing = "back";
	// 	rockchip,camera-module-name = "default";
	// 	rockchip,camera-module-lens-name = "default";
	// 	port {
	// 		sc450ai_out: endpoint {
	// 			remote-endpoint = <&csi_dphy_input0>;
	// 			data-lanes = <1 2 3 4>;
	// 		};
	// 	};
	// };

	// sc450ai_1: sc450ai-1@32 {
	// 	compatible = "smartsens,sc450ai";
	// 	status = "okay";
	// 	reg = <0x32>;
	// 	clocks = <&cru CLK_MIPI1_OUT2IO>;
	// 	clock-names = "xvclk";
	// 	reset-gpios = <&gpio4 RK_PA6 GPIO_ACTIVE_HIGH>;
	// 	pwdn-gpios = <&gpio6 RK_PA0 GPIO_ACTIVE_HIGH>;
	// 	pinctrl-names = "default";
	// 	pinctrl-0 = <&cam_clk1_pins>;
	// 	rockchip,camera-module-index = <1>;
	// 	rockchip,camera-module-facing = "back";
	// 	rockchip,camera-module-name = "default";
	// 	rockchip,camera-module-lens-name = "default";
	// 	port {
	// 		sc450ai_1_out: endpoint {
	// 			remote-endpoint = <&csi_dphy3_input0>;
	// 			data-lanes = <1 2 3 4>;
	// 		};
	// 	};
	// };
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_CIF_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-domains = <&power RV1126B_PD_VDO>;
		pwdn-gpios = <&gpio2 RK_PA6 GPIO_ACTIVE_HIGH>;
		/*reset-gpios = <&gpio2 RK_PC5 GPIO_ACTIVE_HIGH>;*/
		rockchip,grf = <&grf>;
		pinctrl-names = "default";
		/* pinctrl-0 = <&cifm0_dvp_ctl>; */
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "CMK-OT0836-PT2";
		rockchip,camera-module-lens-name = "YT-2929";
		port {
			cam_para_out1: endpoint {
				/* remote-endpoint = <&cif_para_in>; */
			};
		};
	};

	RN6752_1: RN6752@2c {
        compatible = "sony,imx415";
        reg = <0x2c>;
        clocks = <&cru CLK_MIPI0_OUT2IO>;
        clock-names = "xvclk";
        power-domains = <&power RV1126B_PD_VDO>;
        pinctrl-names = "rockchip,camera_default";
        /* pinctrl-0 = <&mipicsi_clk0>; */
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 RK_PA2 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpio3 RK_PB3 GPIO_ACTIVE_LOW>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out1_6752: endpoint {
                 remote-endpoint = <&csi_dphy1_input_6752>; 
                data-lanes = <1 2>;
            };
        };
    };

	RN6752_2: RN6752@2d {
        compatible = "sony,imx415";
        reg = <0x2d>;
        clocks = <&cru CLK_MIPI1_OUT2IO>;
        clock-names = "xvclk";
        power-domains = <&power RV1126B_PD_VDO>;
        pinctrl-names = "rockchip,camera_default";
        /* pinctrl-0 = <&mipicsi_clk0>; */
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 RK_PA3 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_LOW>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out0_6752: endpoint {
                 remote-endpoint = <&mipi_in_ucam0_6752>;
                data-lanes = <1 2>;
            };
        };
    };

	tp2815: tp2815@44 {
        compatible = "techpoint,tp2815";
        reg = <0x44>;
        clocks = <&cru CLK_MIPI2_OUT2IO>;
        clock-names = "xvclk";
        power-domains = <&power RV1126B_PD_VDO>;
        pinctrl-names = "rockchip,camera_default";
        /* pinctrl-0 = <&mipicsi_clk0>; */
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 RK_PA3 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_LOW>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out0_2815: endpoint {
                 remote-endpoint = <&mipi_in_ucam0_2815>;
                data-lanes = <1 2>;
            };
        };
    };

	ov4689: ov4689@5f {
		compatible = "ovti,os04a10";
		reg = <0x5f>;
		clocks = <&cru CLK_MIPI3_OUT2IO>;
		clock-names = "xvclk";
		power-domains = <&power RV1126B_PD_VDO>;
		pinctrl-names = "rockchip,camera_default";
		/* pinctrl-0 = <&mipicsi_clk1>; */
		/*pinctrl-0 = <&mipicsi_clk0>;*/
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		reset-gpios = <&gpio3 RK_PB3 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "JSD3425-C1";
		rockchip,camera-module-lens-name = "JSD3425-C1";
		port {
			ucam_out1: endpoint {
				 remote-endpoint = <&csi_dphy1_input>; 
				data-lanes = <1 2>;
			};
		};

	};

	os04a10: os04a10@5c {
		compatible = "ovti,os04a10";
		reg = <0x5c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		power-domains = <&power RV1126B_PD_VDO>;
		pinctrl-names = "rockchip,camera_default";
		/* pinctrl-0 = <&mipicsi_clk0>; */
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		// pwdn-gpios = <&gpio1 RK_PD4 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "CMK-OT1607-FV1";
		rockchip,camera-module-lens-name = "M12-40IRC-4MP-F16";
		// ir-cut = <&cam_ircut0>;
		port {
			ucam_out0: endpoint {
				/* remote-endpoint = <&mipi_in_ucam0>; */
				data-lanes = <1 2>;
			};
		};
	};
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&mipi2_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				// remote-endpoint = <&csidphy3_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in2>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in0: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		mipi_lvds_sditf: endpoint {
			remote-endpoint = <&isp_vir0>;
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";

	port {
		cif_mipi_in2: endpoint {
			remote-endpoint = <&mipi2_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds2_sditf {
	status = "okay";

	port {
		mipi_lvds2_sditf: endpoint {
			remote-endpoint = <&isp_vir1>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;

		isp_vir0: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf>;
		};
	};
};

&rkisp_vir0_sditf {
	status = "okay";
};

&rkisp_vir1 {
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;

		isp_vir1: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds2_sditf>;
		};
	};
};

&rkisp_vir1_sditf {
	status = "okay";
};

&rkvpss_vir0 {
	status = "okay";
};

&rkvpss_vir1 {
	status = "okay";
};