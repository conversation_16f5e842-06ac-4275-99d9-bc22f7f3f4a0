# 2025-06-24 13:57:23
# run hook: build kernel

[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
arch/arm64/configs/rv1126b_defconfig:344:warning: override: reassigning to symbol EXT4_FS
#
# configuration written to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
  SYNC    include/config/auto.conf.cmd
  CC      scripts/mod/empty.o
  CC      scripts/mod/devicetable-offsets.s
  DTC     arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb
arch/arm64/boot/dts/rockchip/rv1126b.dtsi:1597.25-1609.4: ERROR (phandle_references): /serial@20810000: Reference to non-existent node or label "uart0m0_xfer_pins_pins"

  also defined at arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtsi:552.8-556.3
arch/arm64/boot/dts/rockchip/rv1126b.dtsi:1597.25-1609.4: ERROR (phandle_references): /serial@20810000: Reference to non-existent node or label "uart0m0_ctsn_pins_pins"

  also defined at arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtsi:552.8-556.3
arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtsi:72.21-80.4: ERROR (phandle_references): /wireless-bluetooth: Reference to non-existent node or label "uart0m0_rtsn_pins_pins"

ERROR: Input tree has errors, aborting (use -f to force output)
make[3]: *** [scripts/Makefile.lib:423: arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb] Error 2
make[2]: *** [scripts/Makefile.build:503: arch/arm64/boot/dts/rockchip] Error 2
make[1]: *** [Makefile:1466: rockchip/rv1126bp-evb-v14.dtb] Error 2
make[1]: *** Waiting for unfinished jobs....
  MKELF   scripts/mod/elfconfig.h
  HOSTCC  scripts/mod/modpost.o
  HOSTCC  scripts/mod/sumversion.o
  HOSTCC  scripts/mod/file2alias.o
  HOSTLD  scripts/mod/modpost
  CC      kernel/bounds.s
  CC      arch/arm64/kernel/asm-offsets.s
  CALL    scripts/checksyscalls.sh
make: *** [arch/arm64/Makefile:221: rv1126bp-evb-v14.img] Error 2
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/build-hooks/10-kernel.sh - run_command failed![0m
[31mERROR: exit code 2 from line 36:[0m
[31m    $@[0m
[31mERROR: call stack:[0m
[31m    build-helper: run_command(36)[0m
[31m    10-kernel.sh: do_build(79)[0m
[31m    10-kernel.sh: build_hook(438)[0m
[31m    build-helper: try_func(63)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    10-kernel.sh: main(490)[0m
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/build-hooks/10-kernel.sh - try_func build_hook kernel failed![0m
[31mERROR: exit code 2 from line 67:[0m
[31m    build_hook[0m
[31mERROR: call stack:[0m
[31m    build-helper: try_func(67)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    10-kernel.sh: main(490)[0m
