# 2025-06-24 11:01:40
# run func: build_hook kernel

[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
arch/arm64/configs/rv1126b_defconfig:344:warning: override: reassigning to symbol EXT4_FS
#
# No change to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
  DTC     arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb
arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14-dual-cam.dtsi:27.32-31.6: ERROR (phandle_references): /csi2-dphy0/ports/port@0/endpoint@1: Reference to non-existent node or label "sc450ai_out"

arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14-dual-cam.dtsi:52.29-55.6: ERROR (phandle_references): /csi2-dphy0/ports/port@1/endpoint@0: Reference to non-existent node or label "mipi_csi2_input"

arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14-dual-cam.dtsi:106.33-110.6: ERROR (phandle_references): /csi2-dphy3/ports/port@1/endpoint@0: Reference to non-existent node or label "isp_in"

arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14-dual-cam.dtsi:364.33-367.6: ERROR (phandle_references): /mipi2-csi2/ports/port@0/endpoint@1: Reference to non-existent node or label "csidphy3_out"

  CALL    scripts/checksyscalls.sh
ERROR: Input tree has errors, aborting (use -f to force output)
make[3]: *** [scripts/Makefile.lib:423: arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb] Error 2
make[2]: *** [scripts/Makefile.build:503: arch/arm64/boot/dts/rockchip] Error 2
make[1]: *** [Makefile:1466: rockchip/rv1126bp-evb-v14.dtb] Error 2
make[1]: *** Waiting for unfinished jobs....
make: *** [arch/arm64/Makefile:221: rv1126bp-evb-v14.img] Error 2
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[31mERROR: Running /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/build-hooks/10-kernel.sh - run_command failed![0m
[31mERROR: exit code 2 from line 36:[0m
[31m    $@[0m
[31mERROR: call stack:[0m
[31m    build-helper: run_command(36)[0m
[31m    10-kernel.sh: do_build(79)[0m
[31m    10-kernel.sh: build_hook(438)[0m
[31m    build-helper: try_func(63)[0m
[31m    build-helper: try_hook(96)[0m
[31m    build-helper: source(165)[0m
[31m    10-kernel.sh: main(490)[0m
