#
# Automatically generated file; DO NOT EDIT.
# Rockchip Linux SDK Configuration
#
RK_DEFCONFIG="rockchip_rv1126bp_evb1_v10_defconfig"
RK_CHIP_FAMILY="rv1126b"
RK_CHIP="rv1126bp"
RK_ROOTFS=y
RK_BUILDROOT=y
RK_BUILDROOT_BASE_CFG="rv1126b_ipc"
RK_BUILDROOT_CFG="rockchip_${RK_BUILDROOT_BASE_CFG}"
RK_YOCTO_SUPPORTS=y
# RK_YOCTO is not set
RK_DEBIAN_SUPPORTS=y
# RK_DEBIAN is not set
RK_ROOTFS_SYSTEM="buildroot"
RK_ROOTFS_SYSTEM_BUILDROOT=y
RK_ROOTFS_TYPE="ext4"
RK_ROOTFS_EXT4=y
# RK_ROOTFS_EXT2 is not set
# RK_ROOTFS_SQUASHFS is not set
# RK_ROOTFS_BTRFS is not set
# RK_ROOTFS_F2FS is not set
# RK_ROOTFS_UBI is not set
# RK_ROOTFS_EROFS is not set
# RK_ROOTFS_ROMFS is not set
# RK_ROOTFS_CPIO is not set
# RK_ROOTFS_CPIO_GZ is not set

#
# Post rootfs installs
#
# RK_ROOTFS_OVERLAY is not set
RK_ROOTFS_LD_CACHE=y
RK_ROOTFS_HOSTNAME_DEFAULT=y
# RK_ROOTFS_HOSTNAME_ORIGINAL is not set
# RK_ROOTFS_HOSTNAME_CUSTOM is not set
RK_ROOTFS_LOCALE_DEFAULT=y
# RK_ROOTFS_LOCALE_ORIGINAL is not set
# RK_ROOTFS_LOCALE_CUSTOM is not set
# RK_ROOTFS_INSTALL_MODULES is not set
RK_ROOTFS_STRIP_MODULES=y
# RK_ROOTFS_ASYNC_COMMIT is not set
# RK_ROOTFS_DEBUG_INFO is not set
RK_ROOTFS_UDEV_RULES=y
# RK_WIFIBT is not set
RK_DISK_HELPERS_DEFAULT=y
# RK_DISK_HELPERS_MOUNTALL is not set
# RK_DISK_HELPERS_RESIZEALL is not set
# RK_DISK_HELPERS_DISABLED is not set
# RK_DISK_AUTO_FORMAT is not set
# RK_DISK_SKIP_FSCK is not set
RK_ROOTFS_LOG_GUARDIAN=y
RK_ROOTFS_LOG_GUARDIAN_INTERVAL="10m"
RK_ROOTFS_LOG_GUARDIAN_MIN_SIZE="100M"
RK_ROOTFS_LOG_GUARDIAN_LOG_DIRS="/var/log/,/tmp/"
RK_LOADER=y
RK_UBOOT_CFG="rv1126b"
RK_UBOOT_CFG_FRAGMENTS=""
RK_UBOOT_OPTS=""
RK_UBOOT_INI="RV1126BPMINIALL.ini"
RK_UBOOT_TRUST_INI=""
RK_UBOOT_SPL=y
# RK_UBOOT_RAW is not set
RK_UBOOT_ARCH="arm64"
RK_UBOOT_ARM64=y
# RK_UBOOT_ARM32 is not set
# RK_AMP is not set
RK_KERNEL=y
RK_KERNEL_PREFERRED=""
RK_KERNEL_ARCH="arm64"
RK_KERNEL_ARM64=y
# RK_KERNEL_ARM32 is not set
RK_KERNEL_CFG="rv1126b_defconfig"
RK_KERNEL_CFG_FRAGMENTS=""
RK_KERNEL_DTS_NAME="rv1126bp-evb-v14"
RK_KERNEL_DTS_DIR="kernel/arch/arm64/boot/dts/rockchip"
RK_KERNEL_DTS="$RK_KERNEL_DTS_DIR/$RK_KERNEL_DTS_NAME.dts"
RK_KERNEL_DTB="$RK_KERNEL_DTS_DIR/$RK_KERNEL_DTS_NAME.dtb"
RK_KERNEL_IMG="kernel/arch/$RK_KERNEL_ARCH/boot/Image"

#
# Overloaded configs for Recovery
#
RK_KERNEL_RECOVERY_CFG=""
RK_KERNEL_RECOVERY_CFG_FRAGMENTS=""
RK_KERNEL_RECOVERY_DTS_NAME=""
RK_KERNEL_RECOVERY_LOGO=""
RK_KERNEL_RECOVERY_LOGO_KERNEL=""

#
# Boot (Android-style boot image)
#
RK_BOOT_IMG="boot.img"
# RK_BOOT_COMPRESSED is not set
RK_BOOT_FIT_ITS_NAME="boot.its"
RK_BOOT_FIT_ITS="$RK_CHIP_DIR/$RK_BOOT_FIT_ITS_NAME"
RK_RECOVERY=y
RK_RECOVERY_BASE_CFG="rv1126b"
RK_RECOVERY_CFG="rockchip_${RK_RECOVERY_BASE_CFG}_recovery"
RK_RECOVERY_INITRD_TYPE="cpio.gz"
# RK_RECOVERY_ROMFS is not set
# RK_RECOVERY_CPIO is not set
RK_RECOVERY_CPIO_GZ=y
# RK_RECOVERY_CPIO_XZ is not set
RK_RECOVERY_FIT_ITS_NAME="boot4recovery.its"
RK_RECOVERY_FIT_ITS="$RK_CHIP_DIR/$RK_RECOVERY_FIT_ITS_NAME"
# RK_SECURITY is not set

#
# Extra partitions (oem, userdata, etc.)
#
RK_EXTRA_PARTITION_NUM=2

#
# Extra partition 1
#
RK_EXTRA_PARTITION_1_NAME="oem"
RK_EXTRA_PARTITION_1_DEV="auto"
RK_EXTRA_PARTITION_1_MOUNTPOINT="auto"
RK_EXTRA_PARTITION_1_FSTYPE="ext4"
RK_EXTRA_PARTITION_1_EXT4=y
# RK_EXTRA_PARTITION_1_EXT2 is not set
# RK_EXTRA_PARTITION_1_MSDOS is not set
# RK_EXTRA_PARTITION_1_NTFS is not set
# RK_EXTRA_PARTITION_1_BTRFS is not set
# RK_EXTRA_PARTITION_1_F2FS is not set
# RK_EXTRA_PARTITION_1_EROFS is not set
# RK_EXTRA_PARTITION_1_SQUASHFS is not set
# RK_EXTRA_PARTITION_1_JFFS2 is not set
RK_EXTRA_PARTITION_1_OPTIONS="defaults"
RK_EXTRA_PARTITION_1_SRC="normal"
RK_EXTRA_PARTITION_1_SIZE="auto"
# RK_EXTRA_PARTITION_1_BUILTIN is not set
RK_EXTRA_PARTITION_1_FEATURES="${RK_EXTRA_PARTITION_1_BUILTIN:+builtin}"
RK_EXTRA_PARTITION_1_STR="${RK_EXTRA_PARTITION_1_DEV:-auto}:$RK_EXTRA_PARTITION_1_NAME:$RK_EXTRA_PARTITION_1_MOUNTPOINT:$RK_EXTRA_PARTITION_1_FSTYPE:$RK_EXTRA_PARTITION_1_OPTIONS:${RK_EXTRA_PARTITION_1_SRC// /,}:$RK_EXTRA_PARTITION_1_SIZE:$RK_EXTRA_PARTITION_1_FEATURES"

#
# Extra partition 2
#
RK_EXTRA_PARTITION_2_NAME="userdata"
RK_EXTRA_PARTITION_2_DEV="auto"
RK_EXTRA_PARTITION_2_MOUNTPOINT="auto"
RK_EXTRA_PARTITION_2_FSTYPE="ext4"
RK_EXTRA_PARTITION_2_EXT4=y
# RK_EXTRA_PARTITION_2_EXT2 is not set
# RK_EXTRA_PARTITION_2_MSDOS is not set
# RK_EXTRA_PARTITION_2_NTFS is not set
# RK_EXTRA_PARTITION_2_BTRFS is not set
# RK_EXTRA_PARTITION_2_F2FS is not set
# RK_EXTRA_PARTITION_2_EROFS is not set
# RK_EXTRA_PARTITION_2_SQUASHFS is not set
# RK_EXTRA_PARTITION_2_JFFS2 is not set
RK_EXTRA_PARTITION_2_OPTIONS="defaults"
RK_EXTRA_PARTITION_2_SRC="normal"
RK_EXTRA_PARTITION_2_SIZE="auto"
# RK_EXTRA_PARTITION_2_BUILTIN is not set
RK_EXTRA_PARTITION_2_FEATURES="${RK_EXTRA_PARTITION_2_BUILTIN:+builtin}"
RK_EXTRA_PARTITION_2_STR="${RK_EXTRA_PARTITION_2_DEV:-auto}:$RK_EXTRA_PARTITION_2_NAME:$RK_EXTRA_PARTITION_2_MOUNTPOINT:$RK_EXTRA_PARTITION_2_FSTYPE:$RK_EXTRA_PARTITION_2_OPTIONS:${RK_EXTRA_PARTITION_2_SRC// /,}:$RK_EXTRA_PARTITION_2_SIZE:$RK_EXTRA_PARTITION_2_FEATURES"
RK_EXTRA_PARTITION_STR="$RK_EXTRA_PARTITION_1_STR@$RK_EXTRA_PARTITION_2_STR@$RK_EXTRA_PARTITION_3_STR@$RK_EXTRA_PARTITION_4_STR@$RK_EXTRA_PARTITION_5_STR"

#
# Firmware (partition table, misc image, etc.)
#
RK_PARAMETER="parameter.txt"
RK_MISC=y
RK_MISC_BLANK=y
# RK_MISC_RECOVERY is not set
# RK_MISC_CUSTOM is not set
RK_USE_FIT_IMG=y
RK_UPDATE=y
RK_UPDATE_STORAGE_DEFAULT=y
# RK_UPDATE_STORAGE_FLASH is not set
# RK_UPDATE_STORAGE_EMMC is not set
# RK_UPDATE_STORAGE_SDCARD is not set
# RK_UPDATE_STORAGE_SPINOR is not set
# RK_UPDATE_STORAGE_SPINAND is not set
# RK_UPDATE_STORAGE_SATA is not set
# RK_UPDATE_STORAGE_PCIE is not set
# RK_UPDATE_STORAGE_UFS is not set
# RK_UPDATE_STORAGE_RVD is not set
# RK_AB_UPDATE is not set
RK_PACKAGE_FILE_DEFAULT=y
# RK_PACKAGE_FILE_CUSTOM is not set
RK_OTA_PACKAGE_FILE_DEFAULT=y
# RK_OTA_PACKAGE_FILE_CUSTOM is not set

#
# Others configurations
#
RK_OVERLAY=y
RK_NETWORK_CHECK=y
# RK_SAVE_COMMITTED is not set
RK_SAVE_TRACKED=y
# RK_SAVE_ALL is not set
RK_EXTRA_CONFIGS=""
