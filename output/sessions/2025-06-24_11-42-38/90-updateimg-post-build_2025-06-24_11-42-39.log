# 2025-06-24 11:42:39
# run func: post_build_hook updateimg

[35mMaking update image...[0m
[36m==========================================[0m
[36m          Start packing update image[0m
[36m==========================================[0m
[35mGenerating package-file for update:[0m
# NAME	PATH
package-file	package-file
parameter	parameter.txt
bootloader	MiniLoaderAll.bin
uboot	uboot.img
misc	misc.img
boot	boot.img
recovery	recovery.img
backup	RESERVED
rootfs	rootfs.img
oem	oem.img
userdata	userdata.img
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release/output/firmware/update.img for update...[0m
Android Firmware Package Tool v2.29
------ PACKAGE ------
Add file: ./package-file
package-file,Add file: ./package-file done,offset=0x800,size=0xe1,userspace=0x1
Add file: ./parameter.txt
parameter,Add file: ./parameter.txt done,offset=0x1000,size=0x229,userspace=0x1,flash_address=0x00000000
Add file: ./MiniLoaderAll.bin
bootloader,Add file: ./MiniLoaderAll.bin done,offset=0x1800,size=0x9f1c0,userspace=0x13f
Add file: ./uboot.img
uboot,Add file: ./uboot.img done,offset=0xa1000,size=0x400000,userspace=0x800,flash_address=0x00004000
Add file: ./misc.img
misc,Add file: ./misc.img done,offset=0x4a1000,size=0xc000,userspace=0x18,flash_address=0x00006000
Add file: ./boot.img
boot,Add file: ./boot.img done,offset=0x4ad000,size=0xcc0600,userspace=0x1981,flash_address=0x00008000
Add file: ./recovery.img
recovery,Add file: ./recovery.img done,offset=0x116d800,size=0x15da400,userspace=0x2bb5,flash_address=0x00028000
Add file: ./rootfs.img
rootfs,Add file: ./rootfs.img done,offset=0x2748000,size=0x3a91a000,userspace=0x75234,flash_address=0x00078000
Add file: ./oem.img
oem,Add file: ./oem.img done,offset=0x3d062000,size=0x1157000,userspace=0x22ae,flash_address=0x00c78000
Add file: ./userdata.img
userdata,Add file: ./userdata.img done,offset=0x3e1b9000,size=0x800000,userspace=0x1000,flash_address=0x00cb8000
Add CRC...
Make firmware OK!
------ OK ------
********rkImageMaker ver 2.29********
Generating new image, please wait...
Writing head info...
Writing boot file...
Writing firmware...
Generating MD5 data...
MD5 data generated successfully!
New image generated successfully!
[35m
Run 'make edit-package-file' if you want to change the package-file.
[0m
[35mRunning 90-updateimg.sh - build_updateimg succeeded.[0m
