SHELL=/bin/bash
RK_EXTRA_PARTITION_1_NAME=oem
RK_OTA_PACKAGE_FILE_DEFAULT=y
RK_RECOVERY_FIT_ITS_NAME=boot4recovery.its
RK_DEVICE_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip
RK_TOOLS_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/tools
COLORTERM=truecolor
RK_KERNEL_CFG=rv1126b_defconfig
RK_DEBIAN_SUPPORTS=y
RK_LOG_DIR=/work/rv1126BP/rv1126b_linux6.1_release/output/sessions/2025-06-24_09-59-47
RK_DEFCONFIG=rockchip_rv1126bp_evb1_v10_defconfig
LESS=-FX
TERM_PROGRAM_VERSION=1.101.0
RK_KERNEL_ARM64=y
RK_LOG_BASE_DIR=/work/rv1126BP/rv1126b_linux6.1_release/output/log
RK_EXTRA_PARTITION_2_FEATURES=
RK_EXTRA_PARTITION_1_OPTIONS=defaults
RK_CHIP_SCRIPTS_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/scripts
RK_ROOTFS_TYPE=ext4
RK_KERNEL_RECOVERY_CFG_FRAGMENTS=
RK_UBOOT_INI=RV1126BPMINIALL.ini
PARAMETER=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/parameter.txt
RK_UPDATE=y
RK_SDK_DIR=/work/rv1126BP/rv1126b_linux6.1_release
LC_ADDRESS=zh_CN.UTF-8
RK_RECOVERY_FIT_ITS=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/boot4recovery.its
LC_NAME=zh_CN.UTF-8
RK_KERNEL_RECOVERY_LOGO=
RK_ROOTFS_LOCALE_DEFAULT=y
RK_DISK_HELPERS_DEFAULT=y
RK_EXTRA_PARTITION_2_OPTIONS=defaults
RK_SESSION=2025-06-24_09-59-47
LC_MONETARY=zh_CN.UTF-8
RK_OUTDIR=/work/rv1126BP/rv1126b_linux6.1_release/output
RK_SCRIPTS_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/scripts
RK_EXTRA_PARTITION_STR=auto:oem:auto:ext4:defaults:normal:auto:@auto:userdata:auto:ext4:defaults:normal:auto:@@@
RK_ROOTFS_LOG_GUARDIAN_LOG_DIRS=/var/log/,/tmp/
PWD=/work/rv1126BP/rv1126b_linux6.1_release
RK_FINAL_ENV=/work/rv1126BP/rv1126b_linux6.1_release/output/sessions/2025-06-24_09-59-47/final.env
LOGNAME=vis
XDG_SESSION_TYPE=tty
RK_BUILDROOT=y
RK_EXTRA_PARTITION_2_SRC=normal
RK_RECOVERY_CPIO_GZ=y
RK_USE_FIT_IMG=y
PART_TABLE=/work/rv1126BP/rv1126b_linux6.1_release/output/sessions/2025-06-24_09-59-47/part-table
RK_UBOOT_ARM64=y
RK_CHIP=rv1126bp
RK_CONFIG_IN=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/configs/Config.in
RK_BUILD_HELPER=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/scripts/build-helper
VSCODE_GIT_ASKPASS_NODE=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node
RK_RECOVERY_INITRD_TYPE=cpio.gz
RK_RECOVERY_BASE_CFG=rv1126b
RK_EXTRA_PARTITION_1_FSTYPE=ext4
MOTD_SHOWN=pam
RK_KERNEL_DTS_DIR=kernel/arch/arm64/boot/dts/rockchip
LINES=21
HOME=/home/<USER>
RK_KERNEL_CFG_FRAGMENTS=
RK_NETWORK_CHECK=y
RK_MISC=y
RK_ROOTFS_STRIP_MODULES=y
LANG=en_US.UTF-8
LC_PAPER=zh_CN.UTF-8
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
RK_INITIAL_ENV=/work/rv1126BP/rv1126b_linux6.1_release/output/sessions/2025-06-24_09-59-47/initial.env
COLUMNS=124
RK_KERNEL_RECOVERY_CFG=
RK_ROOTFS_UDEV_RULES=y
RK_UPDATE_STORAGE_DEFAULT=y
RK_EXTRA_PARTITION_2_FSTYPE=ext4
RK_BUILDROOT_BASE_CFG=rv1126b_ipc
RK_UBOOT_SPL=y
SSL_CERT_DIR=/usr/lib/ssl/certs
RK_PARTITION_HELPER=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/scripts/partition-helper
HDR_PART=<hidden>
RK_EXTRA_PARTITION_2_DEV=auto
RK_EXTRA_PARTITION_2_NAME=userdata
RK_EXTRA_PARTS_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/extra-parts
GIT_ASKPASS=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/dist/askpass.sh
RK_MISC_BLANK=y
RK_SAVE_TRACKED=y
RK_EXTRA_PARTITION_1_SIZE=auto
RK_ROOTFS_LOG_GUARDIAN_MIN_SIZE=100M
RK_KERNEL_ARCH=arm64
SSH_CONNECTION=************* 62510 ************ 22
RK_FIRMWARE_DIR=/work/rv1126BP/rv1126b_linux6.1_release/output/firmware
RK_EXTRA_PARTITION_1_SRC=normal
RK_ROOTFS_LOG_GUARDIAN=y
RK_KERNEL_DTB=kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb
RK_KERNEL_DTS=kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts
RK_EXTRA_PARTITION_1_DEV=auto
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
RK_PACKAGE_FILE_DEFAULT=y
RK_OWNER=vis
RK_ROOTFS_SYSTEM=buildroot
RK_KBUILD_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/linux-kbuild
RK_BOOT_IMG=boot.img
RK_EXTRA_PART_OUTDIR=/work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts
LESSCLOSE=/usr/bin/lesspipe %s %s
XDG_SESSION_CLASS=user
RK_EXTRA_PARTITION_2_SIZE=auto
RK_UBOOT_CFG_FRAGMENTS=
LC_IDENTIFICATION=zh_CN.UTF-8
TERM=xterm-256color
RK_EXTRA_CONFIGS=
RK_POST_HOOK_DIR=post-hooks
LESSOPEN=| /usr/bin/lesspipe %s
RK_OWNER_UID=1000
USER=vis
GIT_PAGER=cat
RK_EXTRA_PARTITION_2_STR=auto:userdata:auto:ext4:defaults:normal:auto:
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-b7b993e9b1.sock
RK_OVERLAY=y
RK_EXTRA_PARTITION_2_MOUNTPOINT=auto
RK_BUILDROOT_CFG=rockchip_rv1126b_ipc
RK_ROOTFS=y
RK_UBOOT_TRUST_INI=
SHLVL=2
RK_EXTRA_PARTITION_NUM=2
RK_KERNEL_PREFERRED=
RK_KERNEL_VERSION_RAW=6.1
PAGER=cat
LC_TELEPHONE=zh_CN.UTF-8
RK_CHIPS_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chips
RK_EXTRA_PARTITION_1_MOUNTPOINT=auto
RK_ROOTFS_LOG_GUARDIAN_INTERVAL=10m
LC_MEASUREMENT=zh_CN.UTF-8
RK_UBOOT_ARCH=arm64
RK_BOOT_FIT_ITS_NAME=boot.its
XDG_SESSION_ID=1802
RK_CHIP_FAMILY=rv1126b
RK_BUILD_HOOK_DIR=build-hooks
REPO_URL=https://mirrors.tuna.tsinghua.edu.cn/git/git-repo/
RK_BOOT_FIT_ITS=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/boot.its
RK_POST_HELPER=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/scripts/post-helper
RK_DEFCONFIG_LINK=/work/rv1126BP/rv1126b_linux6.1_release/output/defconfig
RK_UBOOT_CFG=rv1126b
RK_KERNEL_DTS_NAME=rv1126bp-evb-v14
RK_ROOTFS_EXT4=y
RK_PARAMETER=parameter.txt
RK_RECOVERY_CFG=rockchip_rv1126b_recovery
XDG_RUNTIME_DIR=/run/user/1000
SSL_CERT_FILE=/usr/lib/ssl/certs/ca-certificates.crt
RK_KERNEL_IMG=kernel/arch/arm64/boot/Image
RK_DATA_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/data
SSH_CLIENT=************* 62510 22
RK_LOADER=y
RK_EXTRA_PARTITION_1_FEATURES=
LC_TIME=zh_CN.UTF-8
LC_ALL=C
RK_EXTRA_PARTITION_2_EXT4=y
VSCODE_GIT_ASKPASS_MAIN=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/dist/askpass-main.js
RK_CHIP_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip
XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop
RK_ROOTFS_SYSTEM_BUILDROOT=y
RK_CUSTOM_ENV=/work/rv1126BP/rv1126b_linux6.1_release/output/sessions/2025-06-24_09-59-47/custom.env
BROWSER=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/browser.sh
PATH=/home/<USER>/bin:/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli:/home/<USER>/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
RK_DEFAULT_TARGET=all
RK_EXTRA_PARTITION_1_STR=auto:oem:auto:ext4:defaults:normal:auto:
RK_CONFIG=/work/rv1126BP/rv1126b_linux6.1_release/output/.config
RK_RECOVERY=y
RK_KERNEL_RECOVERY_DTS_NAME=
RK_ROOTFS_LD_CACHE=y
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
PARTS=0x00002000@0x00004000(uboot),0x00002000@0x00006000(misc),0x00020000@0x00008000(boot),0x00040000@0x00028000(recovery),0x00010000@0x00068000(backup),0x00c00000@0x00078000(rootfs),0x00040000@0x00c78000(oem),-@0x00cb8000(userdata:grow)
RK_YOCTO_SUPPORTS=y
RK_COMMON_DIR=/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common
RK_ROCKDEV_DIR=/work/rv1126BP/rv1126b_linux6.1_release/rockdev
RK_SESSION_DIR=/work/rv1126BP/rv1126b_linux6.1_release/output/sessions
RK_EXTRA_PARTITION_1_EXT4=y
RK_UBOOT_OPTS=
LC_NUMERIC=zh_CN.UTF-8
RK_ROOTFS_HOSTNAME_DEFAULT=y
OLDPWD=/work/rv1126BP/rv1126b_linux6.1_release/output/log
RK_KERNEL_RECOVERY_LOGO_KERNEL=
RK_KERNEL_VERSION=6.1
TERM_PROGRAM=vscode
RK_KERNEL=y
VSCODE_IPC_HOOK_CLI=/run/user/1000/vscode-ipc-af0a6d95-de77-4f2f-97b4-7b10c815c665.sock
BASH_FUNC_warning%%=() {  rk_log 34 "$@"
}
BASH_FUNC_check_config%%=() {  unset missing;
 for var in $@;
 do
 eval [ -z \"\$$var\" ] || continue;
 missing="$missing $var";
 done;
 [ "$missing" ] || return 0;
 notice "Skipping $(basename "${BASH_SOURCE[1]}") - ${FUNCNAME[1]} for missing configs: $missing.";
 return 1
}
BASH_FUNC_error%%=() {  rk_log 91 "$@"
}
BASH_FUNC_rk_partition_id%%=() {  grep -v "^$HDR_PART " "$PART_TABLE" | grep -n -E -m 1 "^$1(|_[a-z]) " | cut -d':' -f1 || true
}
BASH_FUNC_message%%=() {  rk_log 36 "$@"
}
BASH_FUNC_rk_partition_size_sector_to_readable%%=() {  case "${1:-grow}" in 
 - | 0 | grow)
 echo grow;
 return 0
 ;;
 esac;
 SIZE=$(( $1 * 512 ));
 if [ "$SIZE" -lt 1024 ]; then
 echo $SIZE;
 else
 if [ "$SIZE" -ge $(( 1024 * 1024 * 1024 )) ]; then
 echo "$(echo "scale=1; $SIZE / 1024 / 1024 / 1024" | bc | 			sed 's/\.0$//')G";
 else
 if [ "$SIZE" -ge $(( 1024 * 1024 )) ]; then
 echo "$(echo "scale=1; $SIZE / 1024 / 1024" | bc | 			sed 's/\.0$//')M";
 else
 echo "$(echo "scale=1; $SIZE / 1024" | bc | 			sed 's/\.0$//')K";
 fi;
 fi;
 fi
}
BASH_FUNC_usage_oneline%%=() {  printf "%-40s%s\n" "$1" "${*:2}"
}
BASH_FUNC_rk_extra_part_options%%=() {  rk_extra_part_arg ${1:-1} 5 defaults
}
BASH_FUNC_rk_partition_edit%%=() {  TEMP_FILE="$(mktemp)";
 echo "# name size" > "$TEMP_FILE";
 while read NAME SIZE; do
 SIZE_STR=$(rk_partition_size_sector_to_readable $SIZE);
 echo "$NAME $SIZE # $SIZE_STR" >> "$TEMP_FILE";
 done < "$PART_TABLE";
 eval ${EDITOR:-vi} "$TEMP_FILE";
 sed -i -e "/^#/d" -e "s/[ ]*#.*//" "$TEMP_FILE";
 mv "$TEMP_FILE" "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_rk_partition_start%%=() {  OFFSET=0;
 while read NAME SIZE; do
 if echo "$NAME" | grep -qE "^$1(|_[a-z])$"; then
 echo $OFFSET | awk '{printf "0x%08x\n",$1}';
 return 0;
 fi;
 OFFSET=$(( $OFFSET + ${SIZE/-/0} ));
 done < "$PART_TABLE"
}
BASH_FUNC_rk_extra_part_arg%%=() {  PART="$(rk_extra_part_cfg ${1:-1})";
 ARG="$(echo "$PART" | cut -d':' -f${2:-1})";
 echo "${ARG:-$3}"
}
BASH_FUNC_rk_extra_part_src%%=() {  PART_NAME="$(rk_extra_part_name $1)";
 for src in $(rk_extra_part_arg ${1:-1} 6 | tr ',' ' ');
 do
 if [ -z "$src" -o "$src" = empty ]; then
 return 0;
 else
 if echo "$src" | grep -q "^/"; then
 echo "$src";
 else
 if [ -d "$RK_CHIP_DIR/$PART_NAME/$src" ]; then
 echo "$RK_CHIP_DIR/$PART_NAME/$src";
 else
 if [ -d "$RK_CHIP_DIR/$src" ]; then
 echo "$RK_CHIP_DIR/$src";
 else
 if [ -d "$RK_EXTRA_PARTS_DIR/$PART_NAME/$src" ]; then
 echo "$RK_EXTRA_PARTS_DIR/$PART_NAME/$src";
 else
 echo "$RK_EXTRA_PARTS_DIR/$src";
 fi;
 fi;
 fi;
 fi;
 fi;
 done
}
BASH_FUNC_rk_extra_part_cfg%%=() {  [ "$RK_EXTRA_PARTITION_STR" ] || return 0;
 RK_EXTRA_PARTITION_ARRAY=($(echo ${RK_EXTRA_PARTITION_STR//@/ } | 		xargs -n 1 | sort));
 PART_IDX=$(( ${1:-1} - 1 ));
 echo "${RK_EXTRA_PARTITION_ARRAY[$PART_IDX]}"
}
BASH_FUNC_usage_makefile_oneline%%=() {  printf "  %-22s - %s\n" "$(echo "$1" | grep -o "^[^[^:^ ]*")" "${*:2}"
}
BASH_FUNC_rk_extra_part_num%%=() {  echo ${RK_EXTRA_PARTITION_NUM:-0}
}
BASH_FUNC_rk_extra_part_mountpoint%%=() {  MOUNTPOINT="$(rk_extra_part_arg ${1:-1} 3)";
 case "${MOUNTPOINT:-auto}" in 
 auto)
 echo "/$(rk_extra_part_name $@)"
 ;;
 *)
 echo "$MOUNTPOINT"
 ;;
 esac
}
BASH_FUNC_get_toolchain%%=() {  MODULE="$1";
 if [ "$MODULE" = "buildroot" ]; then
 TC_ARCH="arm";
 else
 TC_ARCH="aarch64";
 fi;
 TC_VENDOR="${3-none}";
 TC_OS="${4:-linux}";
 MACHINE=$(uname -m);
 if [ "$MACHINE" != x86_64 ]; then
 notice "Using Non-x86 toolchain for $MODULE!" 1>&2;
 if [ "$TC_ARCH" = aarch64 -a "$MACHINE" != aarch64 ]; then
 echo aarch64-linux-gnu-;
 else
 if [ "$TC_ARCH" = arm -a "$MACHINE" != armv7l ]; then
 echo arm-linux-gnueabihf-;
 fi;
 fi;
 return 0;
 fi;
 if [ "$RK_CHIP_FAMILY" = "rv1126_rv1109" ]; then
 TC_VENDOR=rockchip830;
 fi;
 TC_DIR="$RK_SDK_DIR/prebuilts/gcc/linux-x86/$TC_ARCH";
 if [ "$TC_VENDOR" ]; then
 TC_PATTERN="$TC_ARCH-$TC_VENDOR-$TC_OS-[^-]*-gcc";
 else
 TC_PATTERN="$TC_ARCH-$TC_OS-[^-]*-gcc";
 fi;
 GCC="$(find "$TC_DIR" -name "*gcc" | grep -m 1 "/$TC_PATTERN$" || true)";
 if [ ! -x "$GCC" ]; then
 { 
 error "No prebuilt GCC toolchain for $MODULE!";
 error "Arch: $TC_ARCH";
 error "Vendor: $TC_VENDOR";
 error "OS: $TC_OS"
 } 1>&2;
 exit 1;
 fi;
 echo ${GCC%gcc}
}
BASH_FUNC_rk_extra_part_fakeroot_script%%=() {  echo "$(rk_extra_part_outdir $1).fs"
}
BASH_FUNC_rk_partition_size_kb%%=() {  PART_SIZE="$(rk_partition_size "$1")";
 echo $(( ${PART_SIZE:-0} / 2))
}
BASH_FUNC_rk_partition_size%%=() {  grep -E -m 1 "^$1(|_[a-z]) " "$PART_TABLE" | cut -d' ' -f2 | tr -d '\-' || true
}
BASH_FUNC_load_config%%=() {  [ -r "$RK_CONFIG" ] || return 0;
 for var in $@;
 do
 export "$(grep "^$var=" "$RK_CONFIG" | tr -d '"' || true)" &> /dev/null || true;
 done
}
BASH_FUNC_rk_partition_insert%%=() {  [ "$#" -gt 1 ] || return 1;
 echo $1 | grep -qE "^[0-9]*$" || return 1;
 IDX=$1;
 if [ "$IDX" -lt 1 ]; then
 error "Index should not be less than 1!";
 return 1;
 fi;
 NUM=$(rk_partition_num);
 if [ "$IDX" -gt "$(($NUM + 1))" ]; then
 error "Index should not be greater than $(($NUM + 1))!";
 return 1;
 fi;
 echo $2 | grep -qE "^[a-zA-Z]" || return 1;
 if rk_partition_name $2 &> /dev/null; then
 error "Part already exists ($2)!";
 return 1;
 fi;
 case "${3:-grow}" in 
 0x*)
 SIZE=$3
 ;;
 *)
 SIZE="$(rk_partition_size_readable_to_sector $3)"
 ;;
 esac;
 if [ "$SIZE" = "-" ] && [ "$IDX" -lt "$(( $NUM + 1 ))" ]; then
 error "Only latest part can be unlimited!";
 return 1;
 fi;
 if [ "$IDX" -eq "$(( $NUM + 1 ))" ] && grep -q "\-$" "$PART_TABLE"; then
 error "Cannot insert after unlimited part!";
 return 1;
 fi;
 sed -i "$IDX a$2 $SIZE" "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_rk_partition_del%%=() {  [ "$#" -gt 0 ] || return 1;
 PART_NAME="$(rk_partition_name $1)";
 [ "$PART_NAME" ] || return 1;
 sed -i "/^$PART_NAME /d" "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_rk_extra_part_fstype%%=() {  rk_extra_part_arg ${1:-1} 4 ext4
}
BASH_FUNC_rk_partition_print%%=() {  message "\n==========================================";
 message "          Partition table";
 message "==========================================";
 { 
 OFFSET=0;
 while read NAME SIZE; do
 OFFSET=$(echo $OFFSET | awk '{printf "0x%08x",$1}');
 SIZE_STR=$(rk_partition_size_sector_to_readable $SIZE);
 if [ "$NAME" != "$HDR_PART" ]; then
 NAME=$(echo $NAME | awk '{printf "%12s",$1}');
 message "$NAME at $OFFSET size=$SIZE($SIZE_STR)";
 fi;
 OFFSET=$(( $OFFSET + ${SIZE/-/0} ));
 done < "$PART_TABLE"
 } | sed "=" | sed "N;s/\n/: /";
 echo;
 message "Legacy cmdline:";
 rk_partition_to_cmdline;
 echo
}
BASH_FUNC_ensure_tools%%=() {  for t in "$@";
 do
 if [ "$RK_ROOTFS_PREFER_PREBUILT_TOOLS" ] || [ "$RK_ROOTFS_PREBUILT_TOOLS" ] || [ ! -e "$t" ]; then
 install -v -D -m 0755 "$RK_TOOLS_DIR/armhf/${t##*/}" "$t";
 continue;
 fi;
 if [ ! -e "$t" ]; then
 warning "Unable to install $t!";
 fi;
 done
}
BASH_FUNC_rk_extra_part_mount_dir%%=() {  echo "$(rk_extra_part_outdir $1)-mount"
}
BASH_FUNC_rk_extra_part_size%%=() {  rk_extra_part_arg ${1:-1} 7 auto
}
BASH_FUNC_fatal%%=() {  rk_log 31 "$@"
}
BASH_FUNC_rk_extra_part_outdir%%=() {  echo "$RK_EXTRA_PART_OUTDIR/$(rk_extra_part_name $1)"
}
BASH_FUNC_rroot%%=() {  cd "$RK_SDK_DIR"
}
BASH_FUNC_rk_partition_rename%%=() {  [ "$#" -gt 1 ] || return 1;
 echo $2 | grep -qE "^[a-zA-Z]" || return 1;
 if rk_partition_name $2 &> /dev/null; then
 error "Part already exists ($2)!";
 return 1;
 fi;
 PART_NAME="$(rk_partition_name $1)";
 [ "$PART_NAME" ] || return 1;
 sed -i "s/^$PART_NAME /$2 /" "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_rk_partition_to_cmdline%%=() {  OFFSET=0;
 while read NAME SIZE; do
 case "$SIZE" in 
 -)
 echo "$NAME $OFFSET" | awk '{printf "-@0x%08x(%s:grow)",$2,$1}';
 break
 ;;
 *)
 SIZE=$(rk_partition_size_readable_to_sector $SIZE)
 ;;
 esac;
 if [ "$NAME" != "$HDR_PART" ]; then
 echo "$NAME $OFFSET $(( $SIZE ))" | awk '{printf "0x%08x@0x%08x(%s)",$3,$2,$1}';
 fi;
 OFFSET=$(( $OFFSET + $SIZE ));
 done < "$PART_TABLE" | sed 's/)\([^$]\)/),\1/g'
}
BASH_FUNC_start_log%%=() {  LOG_FILE="$RK_LOG_DIR/${2:-$1_$(date +%F_%H-%M-%S)}.log";
 ln -rsf "$LOG_FILE" "$RK_LOG_DIR/$1.log";
 echo "# $(date +"%F %T")" >> "$LOG_FILE";
 echo "$LOG_FILE"
}
BASH_FUNC_rk_partition_init%%=() {  rk_partition_parse > "$PART_TABLE"
}
BASH_FUNC_rk_partition_resize%%=() {  [ "$#" -gt 1 ] || return 1;
 case "$2" in 
 0x*)
 SIZE=$2
 ;;
 *)
 SIZE="$(rk_partition_size_readable_to_sector $2)"
 ;;
 esac;
 PART_NAME="$(rk_partition_name $1)";
 [ "$PART_NAME" ] || return 1;
 sed -i "s/^$PART_NAME .*/$PART_NAME $SIZE/" "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_finish_build%%=() {  notice "Running $(basename "${BASH_SOURCE[1]}") - ${@:-${FUNCNAME[1]}} succeeded.";
 cd "$RK_SDK_DIR"
}
BASH_FUNC_rk_log%%=() {  LOG_COLOR="$1";
 shift;
 if [ "$1" = "-n" ]; then
 shift;
 LOG_FLAG="-ne";
 else
 LOG_FLAG="-e";
 fi;
 echo $LOG_FLAG "\e[${LOG_COLOR}m$@\e[0m"
}
BASH_FUNC_rscript%%=() {  cd "$RK_SCRIPTS_DIR"
}
BASH_FUNC_rk_extra_part_img%%=() {  echo "$RK_EXTRA_PART_OUTDIR/$(rk_extra_part_name $1).img"
}
BASH_FUNC_rk_partition_create%%=() {  [ "$#" -gt 1 ] || return 1;
 { 
 echo "$HDR_PART $(echo $(( $1 )) | awk '{printf "0x%08x",$1}')";
 shift;
 while [ "$1" ]; do
 NAME=$1;
 shift;
 SIZE="$(rk_partition_size_readable_to_sector $1)";
 [ -z "$1" ] || shift;
 if [ "$1" -a "$SIZE" = "-" ]; then
 error "Only latest part can be unlimited!";
 break;
 fi;
 echo "$NAME $SIZE";
 done
 } > "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_kernel_version_raw%%=() {  [ -d kernel ] || return 0;
 VERSION_KEYS="VERSION PATCHLEVEL";
 VERSION="";
 for k in $VERSION_KEYS;
 do
 v=$(grep "^$k = " kernel/Makefile | cut -d' ' -f3);
 VERSION=${VERSION:+${VERSION}.}$v;
 done;
 echo $VERSION
}
BASH_FUNC_rk_partition_num%%=() {  echo $(( $(cat "$PART_TABLE" | wc -l) - 1 ))
}
BASH_FUNC_rk_extra_part_name%%=() {  rk_extra_part_arg ${1:-1} 2
}
BASH_FUNC_rk_partition_name%%=() {  [ "$#" -eq 1 ] || return 1;
 if ! echo $1 | grep -qE "^[0-9]*$"; then
 if ! grep -q "^$1 " "$PART_TABLE"; then
 error "No such part ($1)!" 1>&2;
 return 1;
 fi;
 echo $1;
 return 0;
 fi;
 IDX=$1;
 if [ "$IDX" -lt 1 ]; then
 error "Index should not be less than 1!" 1>&2;
 return 1;
 fi;
 NUM=$(rk_partition_num);
 if [ "$IDX" -gt "$NUM" ]; then
 error "Index should not be greater than $NUM!" 1>&2;
 return 1;
 fi;
 sed -n "$(($IDX + 1))s/\(^[^ ]*\) .*/\1/p" "$PART_TABLE"
}
BASH_FUNC_rk_partition_size_readable_to_sector%%=() {  SIZE=${1%B};
 case "${SIZE:-grow}" in 
 - | 0 | grow)
 echo '-';
 return 0
 ;;
 0x*)
 echo $SIZE;
 return 0
 ;;
 esac;
 { 
 case "$SIZE" in 
 *K)
 echo "${SIZE%K} * 2" | bc
 ;;
 *M)
 echo "${SIZE%M} * 2 * 1024" | bc
 ;;
 *G)
 echo "${SIZE%G} * 2 * 1024 * 1024" | bc
 ;;
 *)
 echo "$SIZE / 512" | bc
 ;;
 esac
 } | cut -d'.' -f1 | awk '{printf "0x%08x",$1}'
}
BASH_FUNC_notice%%=() {  rk_log 35 "$@"
}
BASH_FUNC_rk_partition_parse%%=() {  PARAMETER="${1:-$RK_CHIP_DIR/$RK_PARAMETER}";
 if [ ! -r "$PARAMETER" ]; then
 error "$PARAMETER not exists!" 1>&2;
 exit 1;
 fi;
 PARTS="$(grep "^CMDLINE:" "$PARAMETER" | grep -o "0x.*")";
 echo "$HDR_PART $(echo $PARTS | awk -F '[@():]' '{print $2}')";
 echo "${PARTS//,/ }" | xargs -n 1 | awk -F '[@():]' '{print $3,$1}'
}
BASH_FUNC_rk_extra_part_builtin%%=() {  rk_extra_part_arg ${1:-1} 8 | grep -wq builtin
}
BASH_FUNC_rk_partition_move%%=() {  [ "$#" -gt 1 ] || return 1;
 echo $2 | grep -qE "^[0-9]*$" || return 1;
 PART_NAME="$(rk_partition_name $2)";
 [ "$PART_NAME" ] || return 1;
 PART_NAME="$(rk_partition_name $1)";
 [ "$PART_NAME" ] || return 1;
 PART=$(sed -n "/^$PART_NAME /p" "$PART_TABLE");
 NUM=$(rk_partition_num);
 if [ "$2" -eq "$NUM" ] && grep -q "\-$" "$PART_TABLE"; then
 error "Cannot move after unlimited part!";
 return 1;
 fi;
 if echo "$PART" | grep -q "\-$"; then
 error "Cannot move unlimited part ($1)!";
 return 1;
 fi;
 sed -i "/^$PART$/d" "$PART_TABLE";
 sed -i "$2 a$PART" "$PART_TABLE";
 rk_partition_save
}
BASH_FUNC_rk_partition_parse_names%%=() {  rk_partition_parse "$1" | grep -v "^$HDR_PART " | cut -d' ' -f1 | xargs
}
BASH_FUNC_rk_extra_part_prepare%%=() {  PART_NAME="$(rk_extra_part_name $1)";
 OUTDIR="$(rk_extra_part_outdir $1)";
 DST="$(rk_extra_part_img $1)";
 MOUNT_DIR="$(rk_extra_part_mount_dir $1)";
 FAKEROOT_SCRIPT="$(rk_extra_part_fakeroot_script $1)";
 SRCS="$(rk_extra_part_src $1)";
 notice "Preparing partiton $PART_NAME";
 rm -rf "$OUTDIR" "$DST" "$FAKEROOT_SCRIPT" "$RK_FIRMWARE_DIR/$(basename "$DST")";
 mkdir -p "$OUTDIR";
 echo "#!/bin/sh -e" > "$FAKEROOT_SCRIPT";
 chmod a+x "$FAKEROOT_SCRIPT";
 for src in $MOUNT_DIR $SRCS;
 do
 [ -d "$src" ] || continue;
 [ "$(ls "$src/")" ] || continue;
 message "Merging $src into $OUTDIR";
 rsync -a "$src/" "$OUTDIR";
 for f in $(ls "$OUTDIR" | grep "\.fs$" || true);
 do
 message "Merging $src/$f into $FAKEROOT_SCRIPT";
 cat "$OUTDIR/$f" >> "$FAKEROOT_SCRIPT";
 rm -f "$OUTDIR/$f";
 done;
 done
}
BASH_FUNC_rout%%=() {  cd "$RK_OUTDIR"
}
BASH_FUNC_rcommon%%=() {  cd "$RK_COMMON_DIR"
}
BASH_FUNC_rchip%%=() {  cd "$(realpath "$RK_CHIP_DIR")"
}
BASH_FUNC_rk_partition_save%%=() {  PARAMETER="${1:-$RK_CHIP_DIR/$RK_PARAMETER}";
 [ -r "$PARAMETER" ] || return 1;
 PARTS=$(rk_partition_to_cmdline);
 [ "$PARTS" ] || return 1;
 sed -i "/^CMDLINE:/s/0x.*/$PARTS/" "$PARAMETER";
 rk_partition_init
}
BASH_FUNC_kernel_version%%=() {  [ -d kernel ] || return 0;
 KERNEL_DIR="$(basename "$(realpath kernel)")";
 case "$KERNEL_DIR" in 
 kernel-*)
 echo ${KERNEL_DIR#kernel-};
 return 0
 ;;
 esac;
 kernel_version_raw
}
BASH_FUNC_rk_extra_part_dev%%=() {  DEV=$(rk_extra_part_arg ${1:-1} 1);
 case "${DEV:-auto}" in 
 /* | *=*)
 echo $DEV
 ;;
 auto)
 echo PARTLABEL=$(rk_extra_part_name $@)
 ;;
 *)
 echo PARTLABEL=$DEV
 ;;
 esac
}
_=/usr/bin/env
