# 2025-06-24 09:59:48
# run hook: build kernel

[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
arch/arm64/configs/rv1126b_defconfig:344:warning: override: reassigning to symbol EXT4_FS
#
# No change to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
  CALL    scripts/checksyscalls.sh
  Image:  resource.img (with rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp) is ready
  Image:  boot.img (with Image  resource.img) is ready
  Image:  zboot.img (with Image.lz4  resource.img) is ready
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[35m+ /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/scripts/mk-fitimage.sh kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/boot.its kernel/arch/arm64/boot/Image kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb kernel/resource.img[0m
FIT description: U-Boot FIT source file for arm
Created:         Tue Jun 24 09:59:50 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Tue Jun 24 09:59:50 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    68267 Bytes = 66.67 KiB = 0.07 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   675a9ca1baf4586f1082d3283148d7ad6c2444f299ca1825afc552ac2c20093f
 Image 1 (kernel)
  Description:  unavailable
  Created:      Tue Jun 24 09:59:50 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13193224 Bytes = 12884.01 KiB = 12.58 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   7fa3965454cc9a632d12a15ead524489f9a874441aaaa674e78d1b0c49c074bf
 Image 2 (resource)
  Description:  unavailable
  Created:      Tue Jun 24 09:59:50 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    106496 Bytes = 104.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   14049bc3a140334347530cfd097d2d0eb69dd59c1e99b13dc7c79f9276b52846
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  FDT:          fdt
[35m+ ln -rsf kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release/output/firmware/boot.img[0m
Not Found io-domains in kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts
[35mRunning 10-kernel.sh - build_kernel succeeded.[0m
