# 2025-06-24 11:42:09
# run func: post_build_hook firmware

[36m==========================================[0m
[36m          Start packing firmwares[0m
[36m==========================================[0m
[36mLinking /work/rv1126BP/rv1126b_linux6.1_release/output/firmware/parameter.txt from /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/parameter.txt...[0m
[35mPreparing partiton oem[0m
[36mMerging /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/extra-parts/oem/normal into /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem[0m
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.fs[0m
Using host tools in /work/rv1126BP/rv1126b_linux6.1_release/buildroot/output/latest/host (except for mke2fs)
Making /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem (auto sized)
Making /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem with size(13660KB)
mke2fs 1.45.5 (07-Jan-2020)
Creating regular file /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img
Creating filesystem with 3415 4k blocks and 3424 inodes

Allocating group tables: 0/1   done                            
Writing inode tables: 0/1   done                            
Creating journal (1024 blocks): done
Copying files into the device: __populate_fs: Could not allocate block in ext2 filesystem while writing file "game_test.gba"
mke2fs: Could not allocate block in ext2 filesystem while populating file system
Retring with increased size....(1/20)
Making /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem with size(17756KB)
mke2fs 1.45.5 (07-Jan-2020)
Creating regular file /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img
Creating filesystem with 4439 4k blocks and 4448 inodes

Allocating group tables: 0/1   done                            
Writing inode tables: 0/1   done                            
Creating journal (1024 blocks): done
Copying files into the device: done
Writing superblocks and filesystem accounting information: 0/1   done

tune2fs 1.47.0 (5-Feb-2023)
Setting maximal mount count to -1
Setting interval between checks to 0 seconds
Generated /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img[0m
[35mPreparing partiton userdata[0m
[36mMerging /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/extra-parts/userdata/normal into /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata[0m
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.fs[0m
Using host tools in /work/rv1126BP/rv1126b_linux6.1_release/buildroot/output/latest/host (except for mke2fs)
Making /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata (auto sized)
Start from 8M for ext4.
Making /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img from /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata with size(8192KB)
mke2fs 1.45.5 (07-Jan-2020)
Creating regular file /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img
Creating filesystem with 2048 4k blocks and 2048 inodes

Allocating group tables: 0/1   done                            
Writing inode tables: 0/1   done                            
Creating journal (1024 blocks): done
Copying files into the device: done
Writing superblocks and filesystem accounting information: 0/1   done

tune2fs 1.47.0 (5-Feb-2023)
Setting maximal mount count to -1
Setting interval between checks to 0 seconds
Generated /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img
[35mDone packing /work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img[0m
[35mRunning mk-extra-parts.sh - build_extra_part succeeded.[0m
[35mPacked files:[0m
MiniLoaderAll.bin(/work/rv1126BP/rv1126b_linux6.1_release/u-boot/rv1126bp_spl_loader_v1.00.101.bin): 637K
boot.img(/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1/boot.img): 13M
misc.img(/work/rv1126BP/rv1126b_linux6.1_release/output/misc.img): 48K
oem.img(/work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/oem.img): 18M
parameter.txt(/work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chips/rv1126b/parameter.txt): 541
recovery.img(/work/rv1126BP/rv1126b_linux6.1_release/output/recovery/ramboot.img): 22M
rootfs.img(/work/rv1126BP/rv1126b_linux6.1_release/buildroot/output/rockchip_rv1126b_ipc/images/rootfs.ext2): 938M
uboot.img(/work/rv1126BP/rv1126b_linux6.1_release/u-boot/uboot.img): 4.0M
update.img(/work/rv1126BP/rv1126b_linux6.1_release/output/update/Image/update.img): 1003M
userdata.img(/work/rv1126BP/rv1126b_linux6.1_release/output/extra-parts/userdata.img): 8.0M
[35mMaking update image...[0m
[36m==========================================[0m
[36m          Start packing update image[0m
[36m==========================================[0m
[35mGenerating package-file for update:[0m
# NAME	PATH
package-file	package-file
parameter	parameter.txt
bootloader	MiniLoaderAll.bin
uboot	uboot.img
misc	misc.img
boot	boot.img
recovery	recovery.img
backup	RESERVED
rootfs	rootfs.img
oem	oem.img
userdata	userdata.img
[35mPacking /work/rv1126BP/rv1126b_linux6.1_release/output/firmware/update.img for update...[0m
Android Firmware Package Tool v2.29
------ PACKAGE ------
Add file: ./package-file
package-file,Add file: ./package-file done,offset=0x800,size=0xe1,userspace=0x1
Add file: ./parameter.txt
parameter,Add file: ./parameter.txt done,offset=0x1000,size=0x229,userspace=0x1,flash_address=0x00000000
Add file: ./MiniLoaderAll.bin
bootloader,Add file: ./MiniLoaderAll.bin done,offset=0x1800,size=0x9f1c0,userspace=0x13f
Add file: ./uboot.img
uboot,Add file: ./uboot.img done,offset=0xa1000,size=0x400000,userspace=0x800,flash_address=0x00004000
Add file: ./misc.img
misc,Add file: ./misc.img done,offset=0x4a1000,size=0xc000,userspace=0x18,flash_address=0x00006000
Add file: ./boot.img
boot,Add file: ./boot.img done,offset=0x4ad000,size=0xcc0600,userspace=0x1981,flash_address=0x00008000
Add file: ./recovery.img
recovery,Add file: ./recovery.img done,offset=0x116d800,size=0x15da400,userspace=0x2bb5,flash_address=0x00028000
Add file: ./rootfs.img
rootfs,Add file: ./rootfs.img done,offset=0x2748000,size=0x3a91a000,userspace=0x75234,flash_address=0x00078000
Add file: ./oem.img
oem,Add file: ./oem.img done,offset=0x3d062000,size=0x1157000,userspace=0x22ae,flash_address=0x00c78000
Add file: ./userdata.img
userdata,Add file: ./userdata.img done,offset=0x3e1b9000,size=0x800000,userspace=0x1000,flash_address=0x00cb8000
Add CRC...
Make firmware OK!
------ OK ------
********rkImageMaker ver 2.29********
Generating new image, please wait...
Writing head info...
Writing boot file...
Writing firmware...
Generating MD5 data...
MD5 data generated successfully!
New image generated successfully!
[35m
Run 'make edit-package-file' if you want to change the package-file.
[0m
[35mRunning mk-updateimg.sh - build_updateimg succeeded.[0m
[36mImages under /work/rv1126BP/rv1126b_linux6.1_release/output/firmware/ are ready![0m
[35mRunning 80-firmware.sh - build_firmware succeeded.[0m
