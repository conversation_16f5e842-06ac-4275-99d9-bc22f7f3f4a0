# 2025-06-24 11:06:28
# run hook: build kernel

[36mToolchain for kernel:[0m
[36m/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-[0m

[36m==========================================[0m
[36m          Start building kernel[0m
[36m==========================================[0m
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126b_defconfig[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
arch/arm64/configs/rv1126b_defconfig:344:warning: override: reassigning to symbol EXT4_FS
#
# No change to .config
#
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[35m+ make -C /work/rv1126BP/rv1126b_linux6.1_release/kernel/ -j13 CROSS_COMPILE=/work/rv1126BP/rv1126b_linux6.1_release/prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu- ARCH=arm64 rv1126bp-evb-v14.img[0m
make: Entering directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
  DTC     arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb
arch/arm64/boot/dts/rockchip/rv1126b.dtsi:3478.5-37: Warning (graph_endpoint): /vop@22150000/port/endpoint@0:remote-endpoint: graph phandle is not valid
  CALL    scripts/checksyscalls.sh
  Image:  resource.img (with rv1126bp-evb-v14.dtb logo.bmp logo_kernel.bmp) is ready
  Image:  boot.img (with Image  resource.img) is ready
  Image:  zboot.img (with Image.lz4  resource.img) is ready
make: Leaving directory '/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1'
[35m+ /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/common/scripts/mk-fitimage.sh kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release/device/rockchip/.chip/boot.its kernel/arch/arm64/boot/Image kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dtb kernel/resource.img[0m
FIT description: U-Boot FIT source file for arm
Created:         Tue Jun 24 11:06:30 2025
 Image 0 (fdt)
  Description:  unavailable
  Created:      Tue Jun 24 11:06:30 2025
  Type:         Flat Device Tree
  Compression:  uncompressed
  Data Size:    67575 Bytes = 65.99 KiB = 0.06 MiB
  Architecture: AArch64
  Load Address: 0xffffff00
  Hash algo:    sha256
  Hash value:   686654b9c77981e350d35b277ad25fa1906e22bc22f6f24ca0ec47ca8e95e211
 Image 1 (kernel)
  Description:  unavailable
  Created:      Tue Jun 24 11:06:30 2025
  Type:         Kernel Image
  Compression:  uncompressed
  Data Size:    13193224 Bytes = 12884.01 KiB = 12.58 MiB
  Architecture: AArch64
  OS:           Linux
  Load Address: 0xffffff01
  Entry Point:  0xffffff01
  Hash algo:    sha256
  Hash value:   7fa3965454cc9a632d12a15ead524489f9a874441aaaa674e78d1b0c49c074bf
 Image 2 (resource)
  Description:  unavailable
  Created:      Tue Jun 24 11:06:30 2025
  Type:         Multi-File Image
  Compression:  uncompressed
  Data Size:    105472 Bytes = 103.00 KiB = 0.10 MiB
  Hash algo:    sha256
  Hash value:   ff35e641a02c035db3020d11c8679b1d642809fc7a70966631abf0fc7817e70e
 Default Configuration: 'conf'
 Configuration 0 (conf)
  Description:  unavailable
  Kernel:       kernel
  FDT:          fdt
[35m+ ln -rsf kernel/boot.img /work/rv1126BP/rv1126b_linux6.1_release/output/firmware/boot.img[0m
Not Found io-domains in kernel/arch/arm64/boot/dts/rockchip/rv1126bp-evb-v14.dts
[35mRunning 10-kernel.sh - build_kernel succeeded.[0m
