SHELL=/bin/bash
COLORTERM=truecolor
LESS=-FX
TERM_PROGRAM_VERSION=1.101.0
LC_ADDRESS=zh_CN.UTF-8
LC_NAME=zh_CN.UTF-8
LC_MONETARY=zh_CN.UTF-8
PWD=/work/rv1126BP/rv1126b_linux6.1_release
LOGNAME=vis
XDG_SESSION_TYPE=tty
VSCODE_GIT_ASKPASS_NODE=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/node
MOTD_SHOWN=pam
HOME=/home/<USER>
LANG=en_US.UTF-8
LC_PAPER=zh_CN.UTF-8
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
SSL_CERT_DIR=/usr/lib/ssl/certs
GIT_ASKPASS=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/dist/askpass.sh
SSH_CONNECTION=************* 62510 ************ 22
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
LESSCLOSE=/usr/bin/lesspipe %s %s
XDG_SESSION_CLASS=user
LC_IDENTIFICATION=zh_CN.UTF-8
TERM=xterm-256color
LESSOPEN=| /usr/bin/lesspipe %s
USER=vis
GIT_PAGER=cat
VSCODE_GIT_IPC_HANDLE=/run/user/1000/vscode-git-b7b993e9b1.sock
SHLVL=2
PAGER=cat
LC_TELEPHONE=zh_CN.UTF-8
LC_MEASUREMENT=zh_CN.UTF-8
XDG_SESSION_ID=1802
REPO_URL=https://mirrors.tuna.tsinghua.edu.cn/git/git-repo/
XDG_RUNTIME_DIR=/run/user/1000
SSL_CERT_FILE=/usr/lib/ssl/certs/ca-certificates.crt
SSH_CLIENT=************* 62510 22
LC_TIME=zh_CN.UTF-8
VSCODE_GIT_ASKPASS_MAIN=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/extensions/git/dist/askpass-main.js
XDG_DATA_DIRS=/usr/local/share:/usr/share:/var/lib/snapd/desktop
BROWSER=/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/helpers/browser.sh
PATH=/home/<USER>/bin:/home/<USER>/.vscode-server/cli/servers/Stable-dfaf44141ea9deb3b4096f7cd6d24e00c147a4b1/server/bin/remote-cli:/home/<USER>/bin:/home/<USER>/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/1000/bus
LC_NUMERIC=zh_CN.UTF-8
OLDPWD=/work/rv1126BP/rv1126b_linux6.1_release/kernel-6.1
TERM_PROGRAM=vscode
VSCODE_IPC_HOOK_CLI=/run/user/1000/vscode-ipc-af0a6d95-de77-4f2f-97b4-7b10c815c665.sock
BASH_FUNC_warning%%=() {  rk_log 34 "$@"
}
BASH_FUNC_check_config%%=() {  unset missing;
 for var in $@;
 do
 eval [ -z \"\$$var\" ] || continue;
 missing="$missing $var";
 done;
 [ "$missing" ] || return 0;
 notice "Skipping $(basename "${BASH_SOURCE[1]}") - ${FUNCNAME[1]} for missing configs: $missing.";
 return 1
}
BASH_FUNC_error%%=() {  rk_log 91 "$@"
}
BASH_FUNC_message%%=() {  rk_log 36 "$@"
}
BASH_FUNC_usage_oneline%%=() {  printf "%-40s%s\n" "$1" "${*:2}"
}
BASH_FUNC_usage_makefile_oneline%%=() {  printf "  %-22s - %s\n" "$(echo "$1" | grep -o "^[^[^:^ ]*")" "${*:2}"
}
BASH_FUNC_get_toolchain%%=() {  MODULE="$1";
 if [ "$MODULE" = "buildroot" ]; then
 TC_ARCH="arm";
 else
 TC_ARCH="aarch64";
 fi;
 TC_VENDOR="${3-none}";
 TC_OS="${4:-linux}";
 MACHINE=$(uname -m);
 if [ "$MACHINE" != x86_64 ]; then
 notice "Using Non-x86 toolchain for $MODULE!" 1>&2;
 if [ "$TC_ARCH" = aarch64 -a "$MACHINE" != aarch64 ]; then
 echo aarch64-linux-gnu-;
 else
 if [ "$TC_ARCH" = arm -a "$MACHINE" != armv7l ]; then
 echo arm-linux-gnueabihf-;
 fi;
 fi;
 return 0;
 fi;
 if [ "$RK_CHIP_FAMILY" = "rv1126_rv1109" ]; then
 TC_VENDOR=rockchip830;
 fi;
 TC_DIR="$RK_SDK_DIR/prebuilts/gcc/linux-x86/$TC_ARCH";
 if [ "$TC_VENDOR" ]; then
 TC_PATTERN="$TC_ARCH-$TC_VENDOR-$TC_OS-[^-]*-gcc";
 else
 TC_PATTERN="$TC_ARCH-$TC_OS-[^-]*-gcc";
 fi;
 GCC="$(find "$TC_DIR" -name "*gcc" | grep -m 1 "/$TC_PATTERN$" || true)";
 if [ ! -x "$GCC" ]; then
 { 
 error "No prebuilt GCC toolchain for $MODULE!";
 error "Arch: $TC_ARCH";
 error "Vendor: $TC_VENDOR";
 error "OS: $TC_OS"
 } 1>&2;
 exit 1;
 fi;
 echo ${GCC%gcc}
}
BASH_FUNC_load_config%%=() {  [ -r "$RK_CONFIG" ] || return 0;
 for var in $@;
 do
 export "$(grep "^$var=" "$RK_CONFIG" | tr -d '"' || true)" &> /dev/null || true;
 done
}
BASH_FUNC_ensure_tools%%=() {  for t in "$@";
 do
 if [ "$RK_ROOTFS_PREFER_PREBUILT_TOOLS" ] || [ "$RK_ROOTFS_PREBUILT_TOOLS" ] || [ ! -e "$t" ]; then
 install -v -D -m 0755 "$RK_TOOLS_DIR/armhf/${t##*/}" "$t";
 continue;
 fi;
 if [ ! -e "$t" ]; then
 warning "Unable to install $t!";
 fi;
 done
}
BASH_FUNC_fatal%%=() {  rk_log 31 "$@"
}
BASH_FUNC_rroot%%=() {  cd "$RK_SDK_DIR"
}
BASH_FUNC_start_log%%=() {  LOG_FILE="$RK_LOG_DIR/${2:-$1_$(date +%F_%H-%M-%S)}.log";
 ln -rsf "$LOG_FILE" "$RK_LOG_DIR/$1.log";
 echo "# $(date +"%F %T")" >> "$LOG_FILE";
 echo "$LOG_FILE"
}
BASH_FUNC_finish_build%%=() {  notice "Running $(basename "${BASH_SOURCE[1]}") - ${@:-${FUNCNAME[1]}} succeeded.";
 cd "$RK_SDK_DIR"
}
BASH_FUNC_rk_log%%=() {  LOG_COLOR="$1";
 shift;
 if [ "$1" = "-n" ]; then
 shift;
 LOG_FLAG="-ne";
 else
 LOG_FLAG="-e";
 fi;
 echo $LOG_FLAG "\e[${LOG_COLOR}m$@\e[0m"
}
BASH_FUNC_rscript%%=() {  cd "$RK_SCRIPTS_DIR"
}
BASH_FUNC_kernel_version_raw%%=() {  [ -d kernel ] || return 0;
 VERSION_KEYS="VERSION PATCHLEVEL";
 VERSION="";
 for k in $VERSION_KEYS;
 do
 v=$(grep "^$k = " kernel/Makefile | cut -d' ' -f3);
 VERSION=${VERSION:+${VERSION}.}$v;
 done;
 echo $VERSION
}
BASH_FUNC_notice%%=() {  rk_log 35 "$@"
}
BASH_FUNC_rout%%=() {  cd "$RK_OUTDIR"
}
BASH_FUNC_rcommon%%=() {  cd "$RK_COMMON_DIR"
}
BASH_FUNC_rchip%%=() {  cd "$(realpath "$RK_CHIP_DIR")"
}
BASH_FUNC_kernel_version%%=() {  [ -d kernel ] || return 0;
 KERNEL_DIR="$(basename "$(realpath kernel)")";
 case "$KERNEL_DIR" in 
 kernel-*)
 echo ${KERNEL_DIR#kernel-};
 return 0
 ;;
 esac;
 kernel_version_raw
}
_=/usr/bin/env
