// SPDX-License-Identifier: GPL-2.0
/*
 * techpoint dev driver
 *
 * Copyright (C) 2021 Rockchip Electronics Co., Ltd.
 *
 * V0.0X01.0X00 first version.
 */

#include "techpoint_dev.h"
#include "techpoint_tp9930.h"
#include "techpoint_tp2855.h"
#include "techpoint_tp2815.h"

#if ENABLE_2815_SUPPORT
struct i2c_client *g_client = NULL;
int tp28xx_write_reg(u8 reg, u8 val)
{
	int ret = 0;
	ret = techpoint_write_reg(g_client, reg, val);
	if (ret) {
		dev_err(&g_client->dev, "%s failed !\n", __func__);
	}
	return ret;
}
u8 tp28xx_read_reg(u8 reg)
{
	unsigned char val = 0;
	techpoint_read_reg(g_client, reg, &val);
	return val;
}

/////////////////////////////////////////////////////////
//
//
/////////////////////////////////////////////////////////
//2020-10-19
// 1. update CVBS output from 1920x480i/576i to 720x480i/576i.
//
//2020-11-02
// 1. update reg0x45=0x01 to make PTZ output pin enable, else it is floating.
//
enum{
    CH_1=0,   //
    CH_2=1,   //
    CH_3=2,   //
    CH_4=3,   //   
    CH_ALL=4,   //     
    MIPI_PAGE=8,
};
enum{
    STD_TVI, //TVI
    STD_HDA, //AHD
};
enum{
    PAL,
    NTSC,
    HD25,  //720p25
    HD30,  //720p30
    FHD25, //1080p25
    FHD30, //1080p30
    FHD50, //1080p50
    FHD60, //1080p60   
	PAL1920,
};
enum{
    MIPI_4CH4LANE_297M, //up to 4x720p25/30
    MIPI_4CH4LANE_594M, //up to 4x1080p25/30
    MIPI_4CH2LANE_594M, //up to 4x720pp25/30  
};
void TP2855_decoder_init(unsigned char ch,unsigned char fmt,unsigned char std);
void TP2855_mipi_out(unsigned char output, unsigned char fmt);
void TP2855_common_init(u32 width, u32 height)
{
	if(width == 1920 && height == 1080)
	{
		TP2855_decoder_init(CH_ALL, FHD25, STD_HDA);
		TP2855_mipi_out(MIPI_4CH4LANE_594M, FHD25);
	}
	else if(width == 720 && (height == 288 || height == 576))
	{
		TP2855_decoder_init(CH_ALL, PAL, STD_HDA);
		TP2855_mipi_out(MIPI_4CH4LANE_297M, PAL);
	}
	else if(width == 1920 && (height == 288 || height == 576))
	{
		TP2855_decoder_init(CH_ALL, PAL1920, STD_HDA);
		TP2855_mipi_out(MIPI_4CH4LANE_297M, PAL);
	}
	else if(width == 720 && (height == 240 || height == 480))
	{
		TP2855_decoder_init(CH_ALL, NTSC, STD_HDA);
		TP2855_mipi_out(MIPI_4CH4LANE_297M, NTSC);
	}
	else
	{
		TP2855_decoder_init(CH_ALL, HD25, STD_HDA);
		TP2855_mipi_out(MIPI_4CH4LANE_297M, HD25);
	}

}
/////////////////////////////////
//ch: video channel
//fmt: PAL/NTSC/HD25/HD30...
//std: STD_TVI/STD_HDA
////////////////////////////////
void TP2855_decoder_init(unsigned char ch,unsigned char fmt,unsigned char std)
{
	unsigned char tmp;
	const unsigned char SYS_MODE[5]={0x01,0x02,0x04,0x08,0x0f};
	tp28xx_write_reg(0x40, 0x08);
	tp28xx_write_reg(0x23, 0x02);
	tp28xx_write_reg(0x40, ch);
	tp28xx_write_reg(0x45, 0x01);	
	if(PAL == fmt)
	{
#if 1//new sdk
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);

		//tp28xx_write_reg(0x06, 0x32);
		tp28xx_write_reg(0x02, 0x47);
		//tp28xx_write_reg(0x07, 0x80);
		//tp28xx_write_reg(0x0b, 0x80);
		tp28xx_write_reg(0x0c, 0x13);
		tp28xx_write_reg(0x0d, 0x51);
		tp28xx_write_reg(0x15, 0x13);
		tp28xx_write_reg(0x16, 0x18);
		tp28xx_write_reg(0x17, 0xa0);
		tp28xx_write_reg(0x18, 0x17);
		tp28xx_write_reg(0x19, 0x20);
		tp28xx_write_reg(0x1a, 0x15);
		tp28xx_write_reg(0x1c, 0x06);
		tp28xx_write_reg(0x1d, 0xf0);
		tp28xx_write_reg(0x20, 0x48);
		tp28xx_write_reg(0x21, 0x84);
		tp28xx_write_reg(0x22, 0x37);
		tp28xx_write_reg(0x23, 0x3f);
		tp28xx_write_reg(0x2b, 0x70);
		tp28xx_write_reg(0x2c, 0x2a);
		tp28xx_write_reg(0x2d, 0x4b);
		tp28xx_write_reg(0x2e, 0x56);
		tp28xx_write_reg(0x30, 0x7a);
		tp28xx_write_reg(0x31, 0x4a);
		tp28xx_write_reg(0x32, 0x4d);
		tp28xx_write_reg(0x33, 0xfb);
		tp28xx_write_reg(0x35, 0x65);
		tp28xx_write_reg(0x38, 0x00);
		tp28xx_write_reg(0x39, 0x04);

#else
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
		
		tp28xx_write_reg(0x02, 0x47);
		tp28xx_write_reg(0x0c, 0x13); 
		tp28xx_write_reg(0x0d, 0x51);  

		tp28xx_write_reg(0x15, 0x03);
		tp28xx_write_reg(0x16, 0xf0); 
		tp28xx_write_reg(0x17, 0xa0); 
		tp28xx_write_reg(0x18, 0x17);
		tp28xx_write_reg(0x19, 0x20);
		tp28xx_write_reg(0x1a, 0x15);				
		tp28xx_write_reg(0x1c, 0x06);
		tp28xx_write_reg(0x1d, 0xc0);
	
		tp28xx_write_reg(0x20, 0x48);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x37);
		tp28xx_write_reg(0x23, 0x3f);

		tp28xx_write_reg(0x2b, 0x70);  
		tp28xx_write_reg(0x2c, 0x2a); 
		tp28xx_write_reg(0x2d, 0x4b);
		tp28xx_write_reg(0x2e, 0x56);

		tp28xx_write_reg(0x30, 0x7a);  
		tp28xx_write_reg(0x31, 0x4a); 
		tp28xx_write_reg(0x32, 0x4d);
		tp28xx_write_reg(0x33, 0xfb);	
		
		tp28xx_write_reg(0x35, 0x65); 
		tp28xx_write_reg(0x38, 0x00);				
		tp28xx_write_reg(0x39, 0x04); 
#endif
				
	}
	else if(PAL1920 == fmt)
	{
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);

		tp28xx_write_reg(0x02, 0x47);
		tp28xx_write_reg(0x0c, 0x13);
		tp28xx_write_reg(0x0d, 0x51);


		tp28xx_write_reg(0x15, 0x13);
		tp28xx_write_reg(0x16, 0x76);
		tp28xx_write_reg(0x17, 0x80);
		tp28xx_write_reg(0x18, 0x17);
		tp28xx_write_reg(0x19, 0x20);
		tp28xx_write_reg(0x1a, 0x17);
		tp28xx_write_reg(0x1c, 0x09);
		tp28xx_write_reg(0x1d, 0x48);


		tp28xx_write_reg(0x20, 0x48);
		tp28xx_write_reg(0x21, 0x84);
		tp28xx_write_reg(0x22, 0x37);
		tp28xx_write_reg(0x23, 0x3f);

		tp28xx_write_reg(0x2b, 0x70);
		tp28xx_write_reg(0x2c, 0x2a);
		tp28xx_write_reg(0x2d, 0x64);
		tp28xx_write_reg(0x2e, 0x56);

		tp28xx_write_reg(0x30, 0x7a);
		tp28xx_write_reg(0x31, 0x4a);
		tp28xx_write_reg(0x32, 0x4d);
		tp28xx_write_reg(0x33, 0xf0);

		tp28xx_write_reg(0x35, 0x25);
		tp28xx_write_reg(0x38, 0x00);
		tp28xx_write_reg(0x39, 0x04);
	}
	else if(NTSC == fmt)
	{
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);

		tp28xx_write_reg(0x02, 0x47);
		tp28xx_write_reg(0x0c, 0x13); 
		tp28xx_write_reg(0x0d, 0x50);  

		tp28xx_write_reg(0x15, 0x13);//0x03
		tp28xx_write_reg(0x16, 0x18);//0xd6
		tp28xx_write_reg(0x17, 0xa0); 
		tp28xx_write_reg(0x18, 0x13);//0x12
		tp28xx_write_reg(0x19, 0xf0);
		tp28xx_write_reg(0x1a, 0x05);				
		tp28xx_write_reg(0x1c, 0x06);
		tp28xx_write_reg(0x1d, 0xf0);//0xb4
	
		tp28xx_write_reg(0x20, 0x40);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);

		tp28xx_write_reg(0x2b, 0x70);  
		tp28xx_write_reg(0x2c, 0x2a); 
		tp28xx_write_reg(0x2d, 0x4b);
		tp28xx_write_reg(0x2e, 0x57);

		tp28xx_write_reg(0x30, 0x62);  
		tp28xx_write_reg(0x31, 0xbb); 
		tp28xx_write_reg(0x32, 0x96);
		tp28xx_write_reg(0x33, 0xcb);
		
		tp28xx_write_reg(0x35, 0x65); 
		tp28xx_write_reg(0x38, 0x00);			
		tp28xx_write_reg(0x39, 0x04); 	
	}
	else if(HD25 == fmt)
	{
#if 0//new sdk
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);

		tp28xx_write_reg(0x02, 0x42);
		tp28xx_write_reg(0x07, 0xc0);
		tp28xx_write_reg(0x0b, 0xc0);
		tp28xx_write_reg(0x0c, 0x13);
		tp28xx_write_reg(0x0d, 0x50);
		tp28xx_write_reg(0x15, 0x13);
		tp28xx_write_reg(0x16, 0x15);
		tp28xx_write_reg(0x17, 0x00);
		tp28xx_write_reg(0x18, 0x19);
		tp28xx_write_reg(0x19, 0xd0);
		tp28xx_write_reg(0x1a, 0x25);
		tp28xx_write_reg(0x1c, 0x07);
		tp28xx_write_reg(0x1d, 0xbc);
		tp28xx_write_reg(0x20, 0x30);
		tp28xx_write_reg(0x21, 0x84);
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);
		tp28xx_write_reg(0x2b, 0x60);
		tp28xx_write_reg(0x2c, 0x0a);
		tp28xx_write_reg(0x2d, 0x30);
		tp28xx_write_reg(0x2e, 0x70);
		tp28xx_write_reg(0x30, 0x48);
		tp28xx_write_reg(0x31, 0xbb);
		tp28xx_write_reg(0x32, 0x2e);
		tp28xx_write_reg(0x33, 0x90);
		tp28xx_write_reg(0x35, 0x25);
		tp28xx_write_reg(0x38, 0x00);
		tp28xx_write_reg(0x39, 0x18);
		//def ahd config
		tp28xx_write_reg(0x02, 0x46);
		tp28xx_write_reg(0x0d, 0x71);
		tp28xx_write_reg(0x20, 0x40);
		tp28xx_write_reg(0x21, 0x46);
		tp28xx_write_reg(0x25, 0xfe);
		tp28xx_write_reg(0x26, 0x01);
		tp28xx_write_reg(0x2c, 0x3a);
		tp28xx_write_reg(0x2d, 0x5a);
		tp28xx_write_reg(0x2e, 0x40);
		tp28xx_write_reg(0x30, 0x9e);
		tp28xx_write_reg(0x31, 0x20);
		tp28xx_write_reg(0x32, 0x10);
		tp28xx_write_reg(0x33, 0x90);
#else
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
				
		tp28xx_write_reg(0x02, 0x42);
		tp28xx_write_reg(0x07, 0xc0); 
		tp28xx_write_reg(0x0b, 0xc0);  		
		tp28xx_write_reg(0x0c, 0x13); 
		tp28xx_write_reg(0x0d, 0x50);  

		tp28xx_write_reg(0x15, 0x13);
		tp28xx_write_reg(0x16, 0x15); 
		tp28xx_write_reg(0x17, 0x00); 
		tp28xx_write_reg(0x18, 0x1b);//0x19
		tp28xx_write_reg(0x19, 0xd0);
		tp28xx_write_reg(0x1a, 0x25);			
		tp28xx_write_reg(0x1c, 0x07);  //1280*720, 25fps
		tp28xx_write_reg(0x1d, 0xbc);  //1280*720, 25fps

		tp28xx_write_reg(0x20, 0x30);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);

		tp28xx_write_reg(0x2b, 0x60);  
		tp28xx_write_reg(0x2c, 0x0a); 
		tp28xx_write_reg(0x2d, 0x30);
		tp28xx_write_reg(0x2e, 0x70);

		tp28xx_write_reg(0x30, 0x48);  
		tp28xx_write_reg(0x31, 0xbb); 
		tp28xx_write_reg(0x32, 0x2e);
		tp28xx_write_reg(0x33, 0x90);
		
		tp28xx_write_reg(0x35, 0x25); 
		tp28xx_write_reg(0x38, 0x00);	
		tp28xx_write_reg(0x39, 0x18); 

		if(STD_HDA == std)
		{
    	tp28xx_write_reg(0x02, 0x46);

    	tp28xx_write_reg(0x0d, 0x71);

    	tp28xx_write_reg(0x20, 0x40);
    	tp28xx_write_reg(0x21, 0x46);

    	tp28xx_write_reg(0x25, 0xfe);
    	tp28xx_write_reg(0x26, 0x01);

    	tp28xx_write_reg(0x2c, 0x3a);
    	tp28xx_write_reg(0x2d, 0x5a);
    	tp28xx_write_reg(0x2e, 0x40);

    	tp28xx_write_reg(0x30, 0x9e);
    	tp28xx_write_reg(0x31, 0x20);
    	tp28xx_write_reg(0x32, 0x10);
    	tp28xx_write_reg(0x33, 0x90);
		}	
#endif
	}
	else if(HD30 == fmt)
	{
		tmp = tp28xx_read_reg(0xf5);
		tmp |= SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
				
		tp28xx_write_reg(0x02, 0x42);
		tp28xx_write_reg(0x07, 0xc0); 
		tp28xx_write_reg(0x0b, 0xc0);  		
		tp28xx_write_reg(0x0c, 0x13); 
		tp28xx_write_reg(0x0d, 0x50);  

		tp28xx_write_reg(0x15, 0x13);
		tp28xx_write_reg(0x16, 0x15); 
		tp28xx_write_reg(0x17, 0x00); 
		tp28xx_write_reg(0x18, 0x19);
		tp28xx_write_reg(0x19, 0xd0);
		tp28xx_write_reg(0x1a, 0x25);			
		tp28xx_write_reg(0x1c, 0x06);  //1280*720, 30fps
		tp28xx_write_reg(0x1d, 0x72);  //1280*720, 30fps

		tp28xx_write_reg(0x20, 0x30);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);

		tp28xx_write_reg(0x2b, 0x60);  
		tp28xx_write_reg(0x2c, 0x0a); 
		tp28xx_write_reg(0x2d, 0x30);
		tp28xx_write_reg(0x2e, 0x70);

		tp28xx_write_reg(0x30, 0x48);  
		tp28xx_write_reg(0x31, 0xbb); 
		tp28xx_write_reg(0x32, 0x2e);
		tp28xx_write_reg(0x33, 0x90);
		
		tp28xx_write_reg(0x35, 0x25); 
		tp28xx_write_reg(0x38, 0x00);	
		tp28xx_write_reg(0x39, 0x18); 

		if(STD_HDA == std)
		{
    	tp28xx_write_reg(0x02, 0x46);

    	tp28xx_write_reg(0x0d, 0x70);

    	tp28xx_write_reg(0x20, 0x40);
    	tp28xx_write_reg(0x21, 0x46);

    	tp28xx_write_reg(0x25, 0xfe);
    	tp28xx_write_reg(0x26, 0x01);

    	tp28xx_write_reg(0x2c, 0x3a);
    	tp28xx_write_reg(0x2d, 0x5a);
    	tp28xx_write_reg(0x2e, 0x40);

    	tp28xx_write_reg(0x30, 0x9d);
    	tp28xx_write_reg(0x31, 0xca);
    	tp28xx_write_reg(0x32, 0x01);
    	tp28xx_write_reg(0x33, 0xd0);
		}	
	}
	else if(FHD30 == fmt)
	{
		tmp = tp28xx_read_reg(0xf5);
		tmp &= ~SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
		
		tp28xx_write_reg(0x02, 0x40);
		tp28xx_write_reg(0x07, 0xc0); 
		tp28xx_write_reg(0x0b, 0xc0);  		
		tp28xx_write_reg(0x0c, 0x03); 
		tp28xx_write_reg(0x0d, 0x50);  

		tp28xx_write_reg(0x15, 0x03);
		tp28xx_write_reg(0x16, 0xd2); 
		tp28xx_write_reg(0x17, 0x80); 
		tp28xx_write_reg(0x18, 0x29);
		tp28xx_write_reg(0x19, 0x38);
		tp28xx_write_reg(0x1a, 0x47);				
		tp28xx_write_reg(0x1c, 0x08);  //1920*1080, 30fps
		tp28xx_write_reg(0x1d, 0x98);  //
	
		tp28xx_write_reg(0x20, 0x30);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);

		tp28xx_write_reg(0x2b, 0x60);  
		tp28xx_write_reg(0x2c, 0x0a); 
		tp28xx_write_reg(0x2d, 0x30);
		tp28xx_write_reg(0x2e, 0x70);

		tp28xx_write_reg(0x30, 0x48);  
		tp28xx_write_reg(0x31, 0xbb); 
		tp28xx_write_reg(0x32, 0x2e);
		tp28xx_write_reg(0x33, 0x90);
			
		tp28xx_write_reg(0x35, 0x05);
		tp28xx_write_reg(0x38, 0x00); 
		tp28xx_write_reg(0x39, 0x1C); 	
	
		if(STD_HDA == std)
		{
    			tp28xx_write_reg(0x02, 0x44);

    			tp28xx_write_reg(0x0d, 0x72);
    
    			tp28xx_write_reg(0x15, 0x01);
    			tp28xx_write_reg(0x16, 0xf0);
    
    			tp28xx_write_reg(0x20, 0x38);
    			tp28xx_write_reg(0x21, 0x46);

    			tp28xx_write_reg(0x25, 0xfe);
    			tp28xx_write_reg(0x26, 0x0d);

    			tp28xx_write_reg(0x2c, 0x3a);
    			tp28xx_write_reg(0x2d, 0x54);
    			tp28xx_write_reg(0x2e, 0x40);

    			tp28xx_write_reg(0x30, 0xa5);
    			tp28xx_write_reg(0x31, 0x95);
    			tp28xx_write_reg(0x32, 0xe0);
    			tp28xx_write_reg(0x33, 0x60);
		}
	}
	else if(FHD25 == fmt)
	{
#if 0//new sdk
		tmp = tp28xx_read_reg(0xf5);
		tmp &= ~SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);

		tp28xx_write_reg(0x02, 0x40);
		tp28xx_write_reg(0x07, 0xc0);
		tp28xx_write_reg(0x0b, 0xc0);
		tp28xx_write_reg(0x0c, 0x03);
		tp28xx_write_reg(0x0d, 0x50);
		tp28xx_write_reg(0x15, 0x03);
		tp28xx_write_reg(0x16, 0xd2);
		tp28xx_write_reg(0x17, 0x80);
		tp28xx_write_reg(0x18, 0x29);
		tp28xx_write_reg(0x19, 0x38);
		tp28xx_write_reg(0x1a, 0x47);
		tp28xx_write_reg(0x1c, 0x0a);
		tp28xx_write_reg(0x1d, 0x50);
		tp28xx_write_reg(0x20, 0x30);
		tp28xx_write_reg(0x21, 0x84);
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);
		tp28xx_write_reg(0x2b, 0x60);
		tp28xx_write_reg(0x2c, 0x0a);
		tp28xx_write_reg(0x2d, 0x30);
		tp28xx_write_reg(0x2e, 0x70);
		tp28xx_write_reg(0x30, 0x48);
		tp28xx_write_reg(0x31, 0xbb);
		tp28xx_write_reg(0x32, 0x2e);
		tp28xx_write_reg(0x33, 0x90);
		tp28xx_write_reg(0x35, 0x05);
		tp28xx_write_reg(0x38, 0x00);
		tp28xx_write_reg(0x39, 0x1C);
		//def ahd config
		tp28xx_write_reg(0x02, 0x44);
		tp28xx_write_reg(0x0d, 0x73);
		tp28xx_write_reg(0x15, 0x01);
		tp28xx_write_reg(0x16, 0xf0);
		tp28xx_write_reg(0x20, 0x3c);
		tp28xx_write_reg(0x21, 0x46);
		tp28xx_write_reg(0x25, 0xfe);
		tp28xx_write_reg(0x26, 0x0d);
		tp28xx_write_reg(0x2c, 0x3a);
		tp28xx_write_reg(0x2d, 0x54);
		tp28xx_write_reg(0x2e, 0x40);
		tp28xx_write_reg(0x30, 0xa5);
		tp28xx_write_reg(0x31, 0x86);
		tp28xx_write_reg(0x32, 0xfb);
		tp28xx_write_reg(0x33, 0x60);

#else
		tmp = tp28xx_read_reg(0xf5);
		tmp &= ~SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
		
		tp28xx_write_reg(0x02, 0x40);
		tp28xx_write_reg(0x07, 0xc0); 
		tp28xx_write_reg(0x0b, 0xc0);  		
		tp28xx_write_reg(0x0c, 0x03); 
		tp28xx_write_reg(0x0d, 0x50);  
  		
		tp28xx_write_reg(0x15, 0x03);
		tp28xx_write_reg(0x16, 0xd2); 
		tp28xx_write_reg(0x17, 0x80); 
		tp28xx_write_reg(0x18, 0x2a);//0x29
		tp28xx_write_reg(0x19, 0x38);
		tp28xx_write_reg(0x1a, 0x47);				
  		
		tp28xx_write_reg(0x1c, 0x0a);  //1920*1080, 25fps
		tp28xx_write_reg(0x1d, 0x50);  //
			
		tp28xx_write_reg(0x20, 0x30);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);
  		
		tp28xx_write_reg(0x2b, 0x60);  
		tp28xx_write_reg(0x2c, 0x0a); 
		tp28xx_write_reg(0x2d, 0x30);
		tp28xx_write_reg(0x2e, 0x70);
  		
		tp28xx_write_reg(0x30, 0x48);  
		tp28xx_write_reg(0x31, 0xbb); 
		tp28xx_write_reg(0x32, 0x2e);
		tp28xx_write_reg(0x33, 0x90);
					
		tp28xx_write_reg(0x35, 0x05);
		tp28xx_write_reg(0x38, 0x00); 
		tp28xx_write_reg(0x39, 0x1C); 	

		if(STD_HDA == std)                    
		{                                     
   			tp28xx_write_reg(0x02, 0x44);
   			
   			tp28xx_write_reg(0x0d, 0x73);
   			
   			tp28xx_write_reg(0x15, 0x01);
 			tp28xx_write_reg(0x16, 0xec);//0xf0
   			
   			tp28xx_write_reg(0x20, 0x3c);
   			tp28xx_write_reg(0x21, 0x46);
   			
   			tp28xx_write_reg(0x25, 0xfe);
   			tp28xx_write_reg(0x26, 0x0d);
   			
   			tp28xx_write_reg(0x2c, 0x3a);
   			tp28xx_write_reg(0x2d, 0x54);
   			tp28xx_write_reg(0x2e, 0x40);
   			
   			tp28xx_write_reg(0x30, 0xa5);
   			tp28xx_write_reg(0x31, 0x86);
   			tp28xx_write_reg(0x32, 0xfb);
   			tp28xx_write_reg(0x33, 0x60);
		}
#endif
   			
	}
	else if(FHD60 == fmt)
	{
		tmp = tp28xx_read_reg(0xf5);
		tmp &= ~SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
		
		tp28xx_write_reg(0x02, 0x40);
		tp28xx_write_reg(0x07, 0xc0); 
		tp28xx_write_reg(0x0b, 0xc0);  		
		tp28xx_write_reg(0x0c, 0x03); 
		tp28xx_write_reg(0x0d, 0x50);  

		tp28xx_write_reg(0x15, 0x03);
		tp28xx_write_reg(0x16, 0xf0); 
		tp28xx_write_reg(0x17, 0x80); 
		tp28xx_write_reg(0x18, 0x12);
		tp28xx_write_reg(0x19, 0x38);
		tp28xx_write_reg(0x1a, 0x47);				
		tp28xx_write_reg(0x1c, 0x08);  //
		tp28xx_write_reg(0x1d, 0x96);  //
	
		tp28xx_write_reg(0x20, 0x38);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);

		tp28xx_write_reg(0x27, 0xad);
		    
		tp28xx_write_reg(0x2b, 0x60);  
		tp28xx_write_reg(0x2c, 0x0a); 
		tp28xx_write_reg(0x2d, 0x40);
		tp28xx_write_reg(0x2e, 0x70);

		tp28xx_write_reg(0x30, 0x74);  
		tp28xx_write_reg(0x31, 0x9b); 
		tp28xx_write_reg(0x32, 0xa5);
		tp28xx_write_reg(0x33, 0xe0);
			
		tp28xx_write_reg(0x35, 0x05);
		tp28xx_write_reg(0x38, 0x40); 
		tp28xx_write_reg(0x39, 0x68); 	

	}
	else if(FHD50 == fmt)
	{
		tmp = tp28xx_read_reg(0xf5);
		tmp &= ~SYS_MODE[ch];
		tp28xx_write_reg(0xf5, tmp);
		
		tp28xx_write_reg(0x02, 0x40);
		tp28xx_write_reg(0x07, 0xc0); 
		tp28xx_write_reg(0x0b, 0xc0);  		
		tp28xx_write_reg(0x0c, 0x03); 
		tp28xx_write_reg(0x0d, 0x50);  
  		
		tp28xx_write_reg(0x15, 0x03);
		tp28xx_write_reg(0x16, 0xe2); 
		tp28xx_write_reg(0x17, 0x80); 
		tp28xx_write_reg(0x18, 0x27);
		tp28xx_write_reg(0x19, 0x38);
		tp28xx_write_reg(0x1a, 0x47);				
  		
		tp28xx_write_reg(0x1c, 0x0a);  //
		tp28xx_write_reg(0x1d, 0x4e);  //
			
		tp28xx_write_reg(0x20, 0x38);  
		tp28xx_write_reg(0x21, 0x84); 
		tp28xx_write_reg(0x22, 0x36);
		tp28xx_write_reg(0x23, 0x3c);

		tp28xx_write_reg(0x27, 0xad);
		  		
		tp28xx_write_reg(0x2b, 0x60);  
		tp28xx_write_reg(0x2c, 0x0a); 
		tp28xx_write_reg(0x2d, 0x40);
		tp28xx_write_reg(0x2e, 0x70);
  		
		tp28xx_write_reg(0x30, 0x74);  
		tp28xx_write_reg(0x31, 0x9b); 
		tp28xx_write_reg(0x32, 0xa5);
		tp28xx_write_reg(0x33, 0xe0);
					
		tp28xx_write_reg(0x35, 0x05);
		tp28xx_write_reg(0x38, 0x40); 
		tp28xx_write_reg(0x39, 0x68); 	

	}		
	
}

void TP2855_mipi_out(unsigned char output, unsigned char fmt)
{
	//mipi setting
	tp28xx_write_reg(0x40, MIPI_PAGE); //MIPI page
    tp28xx_write_reg(0x01, 0xf0);
    tp28xx_write_reg(0x02, 0x01);
    tp28xx_write_reg(0x08, 0x0f);

	if( MIPI_4CH4LANE_594M == output)
	{

            tp28xx_write_reg(0x20, 0x44);
            tp28xx_write_reg(0x34, 0xe4); //
            tp28xx_write_reg(0x15, 0x0C);
            tp28xx_write_reg(0x25, 0x08);
            tp28xx_write_reg(0x26, 0x06);
            tp28xx_write_reg(0x27, 0x11);
            tp28xx_write_reg(0x29, 0x0a);
            tp28xx_write_reg(0x33, 0x07);
            tp28xx_write_reg(0x33, 0x00);
            tp28xx_write_reg(0x14, 0x33);
            tp28xx_write_reg(0x14, 0xb3);
            tp28xx_write_reg(0x14, 0x33);

	
	}
	else if( MIPI_4CH4LANE_297M == output)
	{
			unsigned char tmp;

            tp28xx_write_reg(0x20, 0x44);
            tp28xx_write_reg(0x34, 0xe4); //
            tp28xx_write_reg(0x14, 0x44);
            tp28xx_write_reg(0x15, 0x0d);
            tp28xx_write_reg(0x25, 0x04);
            tp28xx_write_reg(0x26, 0x03);
            tp28xx_write_reg(0x27, 0x09);
            tp28xx_write_reg(0x29, 0x02);
            tp28xx_write_reg(0x33, 0x07);
            tp28xx_write_reg(0x33, 0x00);
            tp28xx_write_reg(0x14, 0xc4);
            tp28xx_write_reg(0x14, 0x44);

			if(fmt == PAL || fmt == NTSC)
			{
				tmp = tp28xx_read_reg(0x04);
				tmp |= 0x08;
				tp28xx_write_reg(0x04, tmp);


				tmp = tp28xx_read_reg(0x21);
				tmp |= 0x34;
				tp28xx_write_reg(0x21, tmp);
			}
	
	}
	else if( MIPI_4CH2LANE_594M == output)
	{

            tp28xx_write_reg(0x20, 0x42);
            tp28xx_write_reg(0x34, 0xe4); //output vin1&vin2
            tp28xx_write_reg(0x15, 0x0c);
            tp28xx_write_reg(0x25, 0x08);
            tp28xx_write_reg(0x26, 0x06);
            tp28xx_write_reg(0x27, 0x11);
            tp28xx_write_reg(0x29, 0x0a);
            tp28xx_write_reg(0x33, 0x07);
            tp28xx_write_reg(0x33, 0x00);
            tp28xx_write_reg(0x14, 0x43);
            tp28xx_write_reg(0x14, 0xc3);
            tp28xx_write_reg(0x14, 0x43);
	
	}


#if 1
	{
		int retry = 20;
		u8 value = 0;
		while(retry>0)
		{
			pr_alert("%s:%d retry = %d\n", __func__, __LINE__, retry);
			tp28xx_write_reg(0x40, 0x00);
			value = tp28xx_read_reg(0x01);
			if((value & 0x78) == 0x78)
			{
				pr_alert("%s:%d get ch0\n", __func__, __LINE__);
				break;
			}
			tp28xx_write_reg(0x40, 0x01);
			value = tp28xx_read_reg(0x01);
			if((value & 0x78) == 0x78)
			{
				pr_alert("%s:%d get ch1\n", __func__, __LINE__);
				break;
			}
			tp28xx_write_reg(0x40, 0x02);
			value = tp28xx_read_reg(0x01);
			if((value & 0x78) == 0x78)
			{
				pr_alert("%s:%d get ch2\n", __func__, __LINE__);
				break;
			}
			tp28xx_write_reg(0x40, 0x03);
			value = tp28xx_read_reg(0x01);
			if((value & 0x78) == 0x78)
			{
				pr_alert("%s:%d get ch3\n", __func__, __LINE__);
				break;
			}
			retry--;
			usleep_range(50000, 51000);
		}
		usleep_range(50000, 51000);
		tp28xx_write_reg(0x40, MIPI_PAGE); //MIPI page
	}
#endif

	/* Enable MIPI CSI2 output */
	tp28xx_write_reg(0x23, 0x02);
	tp28xx_write_reg(0x23, 0x00);
		
}
#endif

static struct semaphore reg_sem;

int techpoint_write_reg(struct i2c_client *client, u8 reg, u8 val)
{
	struct i2c_msg msg;
	u8 buf[2];
	int ret;

	buf[0] = reg & 0xFF;
	buf[1] = val;

	msg.addr = client->addr;
	msg.flags = client->flags;
	msg.buf = buf;
	msg.len = sizeof(buf);

	ret = i2c_transfer(client->adapter, &msg, 1);
	if (ret >= 0) {
		usleep_range(300, 400);
		return 0;
	}

	dev_err(&client->dev,
		"techpoint write reg(0x%x val:0x%x) failed !\n", reg, val);

	return ret;
}

int techpoint_write_array(struct i2c_client *client,
			  const struct regval *regs, int size)
{
	int i, ret = 0;

	i = 0;

	while (i < size) {
		ret = techpoint_write_reg(client, regs[i].addr, regs[i].val);
		if (ret) {
			dev_err(&client->dev, "%s failed !\n", __func__);
			break;
		}
		i++;
	}

	return ret;
}

int techpoint_read_reg(struct i2c_client *client, u8 reg, u8 *val)
{
	struct i2c_msg msg[2];
	u8 buf[1];
	int ret;

	buf[0] = reg & 0xFF;

	msg[0].addr = client->addr;
	msg[0].flags = client->flags;
	msg[0].buf = buf;
	msg[0].len = sizeof(buf);

	msg[1].addr = client->addr;
	msg[1].flags = client->flags | I2C_M_RD;
	msg[1].buf = buf;
	msg[1].len = 1;

	ret = i2c_transfer(client->adapter, msg, 2);
	if (ret >= 0) {
		*val = buf[0];
		return 0;
	}

	dev_err(&client->dev, "techpoint read reg(0x%x) failed !\n", reg);

	return ret;
}

static int check_chip_id(struct techpoint *techpoint)
{
	struct i2c_client *client = techpoint->client;
	struct device *dev = &client->dev;
	unsigned char chip_id_h = 0xFF, chip_id_l = 0xFF;

#if ENABLE_2815_SUPPORT
	chip_id_h = tp28xx_read_reg(CHIP_ID_H_REG);
	chip_id_l = tp28xx_read_reg(CHIP_ID_L_REG);
#else
	techpoint_read_reg(client, CHIP_ID_H_REG, &chip_id_h);
	techpoint_read_reg(client, CHIP_ID_L_REG, &chip_id_l);
#endif
	dev_err(dev, "chip_id_h:0x%2x chip_id_l:0x%2x\n", chip_id_h, chip_id_l);
#if 0
	chip_id_h = TP2855_CHIP_ID_H_VALUE;
	chip_id_l = TP2855_CHIP_ID_L_VALUE;
#endif
	if (chip_id_h == TP9930_CHIP_ID_H_VALUE &&
	    chip_id_l == TP9930_CHIP_ID_L_VALUE) {
		dev_info(&client->dev,
			 "techpoint check chip id CHIP_TP9930 !\n");
		techpoint->chip_id = CHIP_TP9930;
		techpoint->input_type = TECHPOINT_DVP_BT1120;
		return 0;
	} else if (chip_id_h == TP2855_CHIP_ID_H_VALUE &&
		   chip_id_l == TP2855_CHIP_ID_L_VALUE) {
		dev_info(&client->dev,
			 "techpoint check chip id CHIP_TP2855 !\n");
		techpoint->chip_id = CHIP_TP2855;
		techpoint->input_type = TECHPOINT_MIPI;
		return 0;
	} else if (chip_id_h == TP2815_CHIP_ID_H_VALUE &&
		   chip_id_l == TP2815_CHIP_ID_L_VALUE) {
		dev_info(&client->dev,
			 "techpoint check chip id CHIP_TP2815 !\n");
		techpoint->chip_id = CHIP_TP2855;
		techpoint->input_type = TECHPOINT_MIPI;
		return 0;
	} else {
		dev_info(&client->dev, "techpoint check chip id failed !\n");
	}

	return -1;
}

int techpoint_initialize_devices(struct techpoint *techpoint)
{
#if ENABLE_2815_SUPPORT
	g_client = techpoint->client;
#endif
	if (check_chip_id(techpoint))
		return -1;

	if (techpoint->chip_id == CHIP_TP9930) {
		tp9930_initialize(techpoint);
	} else if (techpoint->chip_id == CHIP_TP2855) {
		tp2855_initialize(techpoint);
	}

	sema_init(&reg_sem, 1);

	return 0;
}

static int detect_thread_function(void *data)
{
	struct techpoint *techpoint = (struct techpoint *)data;
	struct i2c_client *client = techpoint->client;
	u8 detect_status = 0, i;
	int need_reset_wait = -1;

	if (techpoint->power_on) {
		down(&reg_sem);
		if (techpoint->chip_id == CHIP_TP9930) {
			tp9930_get_all_input_status(client,
						    techpoint->detect_status);
			for (i = 0; i < TECHPOINT_PAD_MAX; i++)
				tp9930_set_decoder_mode(client, i,
							techpoint->detect_status[i]);
		} else if (techpoint->chip_id == CHIP_TP2855)
			tp2855_get_all_input_status(client,
						    techpoint->detect_status);
		up(&reg_sem);
		techpoint->do_reset = 0;
	}

	while (!kthread_should_stop()) {
		down(&reg_sem);
		if (techpoint->power_on) {
			for (i = 0; i < TECHPOINT_PAD_MAX; i++) {
				if (techpoint->chip_id == CHIP_TP9930)
					detect_status =
					    tp9930_get_channel_input_status
					    (client, i);
				else if (techpoint->chip_id == CHIP_TP2855)
					detect_status =
					    tp2855_get_channel_input_status
					    (client, i);

				if (techpoint->detect_status[i] !=
				    detect_status) {
					if (!detect_status)
						dev_err(&client->dev,
							"detect channel %d video plug out\n",
							i);
					else
						dev_err(&client->dev,
							"detect channel %d video plug in\n",
							i);

					if (techpoint->chip_id == CHIP_TP9930)
						tp9930_set_decoder_mode(client,
									i,
									detect_status);

					techpoint->detect_status[i] =
					    detect_status;
					need_reset_wait = 5;
				}
			}
			if (need_reset_wait > 0) {
				need_reset_wait--;
			} else if (need_reset_wait == 0) {
				need_reset_wait = -1;
				techpoint->do_reset = 1;
				dev_err(&client->dev,
					"trigger reset time up\n");
			}
		}
		up(&reg_sem);
		set_current_state(TASK_INTERRUPTIBLE);
		schedule_timeout(msecs_to_jiffies(200));
	}
	return 0;
}

static int __maybe_unused detect_thread_start(struct techpoint *techpoint)
{
	int ret = 0;
	struct i2c_client *client = techpoint->client;

	techpoint->detect_thread = kthread_create(detect_thread_function,
						  techpoint,
						  "techpoint_kthread");
	if (IS_ERR(techpoint->detect_thread)) {
		dev_err(&client->dev,
			"kthread_create techpoint_kthread failed\n");
		ret = PTR_ERR(techpoint->detect_thread);
		techpoint->detect_thread = NULL;
		return ret;
	}
	wake_up_process(techpoint->detect_thread);
	return ret;
}

static int __maybe_unused detect_thread_stop(struct techpoint *techpoint)
{
	if (techpoint->detect_thread)
		kthread_stop(techpoint->detect_thread);
	techpoint->detect_thread = NULL;
	return 0;
}

static __maybe_unused int auto_detect_channel_fmt(struct techpoint *techpoint)
{
	int ch = 0;
	enum techpoint_support_reso reso = 0xff;
	struct i2c_client *client = techpoint->client;

	down(&reg_sem);

	for (ch = 0; ch < TECHPOINT_PAD_MAX; ch++) {
		if (techpoint->chip_id == CHIP_TP9930) {
			reso = tp9930_get_channel_reso(client, ch);
			tp9930_set_channel_reso(client, ch, reso);
		} else if (techpoint->chip_id == CHIP_TP2855) {
			reso = tp2855_get_channel_reso(client, ch);
			tp2855_set_channel_reso(client, ch, reso);
		}
	}

	up(&reg_sem);

	return 0;
}

void __techpoint_get_vc_fmt_inf(struct techpoint *techpoint,
				struct rkmodule_vc_fmt_info *inf)
{
	int ch = 0;
	int val = 0;
	enum techpoint_support_reso reso = 0xff;
	struct i2c_client *client = techpoint->client;

	down(&reg_sem);

	for (ch = 0; ch < TECHPOINT_PAD_MAX; ch++) {
		if (techpoint->chip_id == CHIP_TP9930) {
			reso = tp9930_get_channel_reso(client, ch);
		} else if (techpoint->chip_id == CHIP_TP2855) {
			reso = tp2855_get_channel_reso(client, ch);
		}
		val = reso;
		switch (val) {
		case TECHPOINT_S_RESO_1080P_30:
			inf->width[ch] = 1920;
			inf->height[ch] = 1080;
			inf->fps[ch] = 30;
			break;
		case TECHPOINT_S_RESO_1080P_25:
			inf->width[ch] = 1920;
			inf->height[ch] = 1080;
			inf->fps[ch] = 25;
			break;
		case TECHPOINT_S_RESO_720P_30:
			inf->width[ch] = 1280;
			inf->height[ch] = 720;
			inf->fps[ch] = 30;
			break;
		case TECHPOINT_S_RESO_720P_25:
			inf->width[ch] = 1280;
			inf->height[ch] = 720;
			inf->fps[ch] = 25;
			break;
		default:
#if DEF_1080P
			inf->width[ch] = 1920;
			inf->height[ch] = 1080;
			inf->fps[ch] = 25;
#else
			inf->width[ch] = 1280;
			inf->height[ch] = 720;
			inf->fps[ch] = 25;
#endif
			break;
		}
	}

	up(&reg_sem);
}

void techpoint_get_vc_fmt_inf(struct techpoint *techpoint,
			      struct rkmodule_vc_fmt_info *inf)
{
	down(&reg_sem);

	if (techpoint->chip_id == CHIP_TP9930) {
		tp9930_pll_reset(techpoint->client);
	}

	techpoint_write_array(techpoint->client,
			      techpoint->cur_video_mode->common_reg_list,
			      techpoint->cur_video_mode->common_reg_size);

	if (techpoint->chip_id == CHIP_TP9930) {
		tp9930_do_reset_pll(techpoint->client);
	}

	up(&reg_sem);

	__techpoint_get_vc_fmt_inf(techpoint, inf);
}

void techpoint_get_vc_hotplug_inf(struct techpoint *techpoint,
				  struct rkmodule_vc_hotplug_info *inf)
{
	int ch = 0;
	int detect_status = 0;
	struct i2c_client *client = techpoint->client;

	memset(inf, 0, sizeof(*inf));

	down(&reg_sem);

	for (ch = 0; ch < 4; ch++) {
		if (techpoint->chip_id == CHIP_TP9930)
			detect_status =
			    tp9930_get_channel_input_status(client, ch);
		else if (techpoint->chip_id == CHIP_TP2855)
			detect_status =
			    tp2855_get_channel_input_status(client, ch);

		inf->detect_status |= detect_status << ch;
	}

	up(&reg_sem);
}

void techpoint_set_quick_stream(struct techpoint *techpoint, u32 stream)
{

	if (techpoint->chip_id == CHIP_TP2855) {
		tp2855_set_quick_stream(techpoint->client, stream);
	}
}

int techpoint_start_video_stream(struct techpoint *techpoint)
{
	int ret = 0;
	struct i2c_client *client = techpoint->client;

	down(&reg_sem);
	if (techpoint->chip_id == CHIP_TP9930) {
		tp9930_pll_reset(techpoint->client);
	}
	up(&reg_sem);

#if ENABLE_2815_SUPPORT
	g_client = client;
	TP2855_common_init(techpoint->cur_video_mode->width, techpoint->cur_video_mode->height);
	ret = 0;
#else
	ret = techpoint_write_array(techpoint->client,
				    techpoint->cur_video_mode->common_reg_list,
				    techpoint->cur_video_mode->common_reg_size);
	if (ret) {
		dev_err(&client->dev,
			"techpoint_start_video_stream common_reg_list failed");
		return ret;
	}
#endif

	down(&reg_sem);
	if (techpoint->chip_id == CHIP_TP9930) {
		tp9930_do_reset_pll(techpoint->client);
	}
	up(&reg_sem);

#if !ENABLE_CUSTOM_REGISTER_LIST
	usleep_range(500 * 1000, 1000 * 1000);
	auto_detect_channel_fmt(techpoint);
#endif

#if ENABLE_DETECT_THREAD
	detect_thread_start(techpoint);
#endif

	usleep_range(500 * 1000, 1000 * 1000);

	return 0;
}

int techpoint_stop_video_stream(struct techpoint *techpoint)
{
	detect_thread_stop(techpoint);

	if (techpoint->chip_id == CHIP_TP9930) {

	} else if (techpoint->chip_id == CHIP_TP2855) {

	}
	return 0;
}
