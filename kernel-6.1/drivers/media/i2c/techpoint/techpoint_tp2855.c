// SPDX-License-Identifier: GPL-2.0
/*
 * techpoint techpoint lib
 *
 * Copyright (C) 2021 Rockchip Electronics Co., Ltd.
 */

#include "techpoint_tp2855.h"
#include "techpoint_dev.h"

#if ENABLE_CUSTOM_REGISTER_LIST
static __maybe_unused const struct regval common_setting_297M_720p_regs[] = {
	{0x40, 0x08}, //MIPI_PAGE
	{0x23, 0x02}, //disable mipi output
	{0x40, 0x04},
	{0xf5, 0xff},
	{0x02, 0x42},
	{0x07, 0xc0},
	{0x0b, 0xc0},
	{0x0c, 0x13},
	{0x0d, 0x50},
	{0x0f, 0xfc},
	{0x10, 0x00},
	{0x11, 0x50},
	{0x12, 0x50},
	{0x13, 0x14},
	{0x14, 0x05},
	{0x15, 0x13},
	{0x16, 0x15},
	{0x17, 0x00},
	{0x18, 0x19},
	{0x19, 0xd0},
	{0x1a, 0x25},
	{0x1c, 0x07},  //1280*720, 25fps
	{0x1d, 0xbc},  //1280*720, 25fps
	{0x20, 0x30},
	{0x21, 0x84},
	{0x22, 0x36},
	{0x23, 0x3c},
	{0x2b, 0x60},
	{0x2c, 0x0a},
	{0x2d, 0x30},
	{0x2e, 0x70},
	{0x30, 0x48},
	{0x31, 0xbb},
	{0x32, 0x2e},
	{0x33, 0x90},
	{0x35, 0x25},
	{0x38, 0x00},
	{0x39, 0x18},
	{0x02, 0x46},
	{0x0d, 0x71},
	{0x20, 0x40},
	{0x21, 0x46},
	{0x25, 0xfe},
	{0x26, 0x01},
	{0x2c, 0x3a},
	{0x2d, 0x5a},
	{0x2e, 0x40},
	{0x30, 0x9e},
	{0x31, 0x20},
	{0x32, 0x10},
	{0x33, 0x90},
	//{0x2a, 0x3c},//blue screen
	{0x40, 0x00},
	{0x0f, 0xcc},
	{0x40, 0x08}, //MIPI page
	{0x01, 0xf0},
	{0x02, 0x01},
	{0x08, 0x0f},
	{0x20, 0x44},
	{0x34, 0xe4}, //
	{0x14, 0x44},
	{0x15, 0x0d},
	{0x25, 0x04},
	{0x26, 0x03},
	{0x27, 0x09},
	{0x29, 0x02},
	{0x33, 0x07},
	{0x33, 0x00},
	{0x14, 0xc4},
	{0x14, 0x44},
	{0x23, 0x02}, //vi test ok
	{0x23, 0x00},
};

static __maybe_unused const struct regval common_setting_594M_1080p_regs[] = {
	{0x40, 0x08}, //MIPI_PAGE
	{0x23, 0x02}, //disable mipi output
	{0x40, 0x04},
	{0xf5, 0xf0},
	{0x02, 0x40},
	{0x07, 0xc0},
	{0x0b, 0xc0},
	{0x0c, 0x03},
	{0x0d, 0x50},
	{0x10, 0x08},
	{0x11, 0x4A},
	{0x12, 0x37},
	{0x13, 0x14},
	{0x14, 0x00},
	{0x15, 0x03},
	{0x16, 0xd2},
	{0x17, 0x80},
	{0x18, 0x29},
	{0x19, 0x38},
	{0x1a, 0x47},
	{0x1c, 0x0a},  //1920*1080, 25fps
	{0x1d, 0x50},  //
	{0x20, 0x30},
	{0x21, 0x84},
	{0x22, 0x36},
	{0x23, 0x3c},
	{0x2b, 0x60},
	{0x2c, 0x0a},
	{0x2d, 0x30},
	{0x2e, 0x70},
	{0x30, 0x48},
	{0x31, 0xbb},
	{0x32, 0x2e},
	{0x33, 0x90},
	{0x35, 0x05},
	{0x38, 0x00},
	{0x39, 0x1C},
	{0x02, 0x44},
	{0x0d, 0x73},
	{0x15, 0x01},
	{0x16, 0xf0},
	{0x20, 0x3c},
	{0x21, 0x46},
	{0x25, 0xfe},
	{0x26, 0x0d},
	{0x2c, 0x3a},
	{0x2d, 0x54},
	{0x2e, 0x40},
	{0x30, 0xa5},
	{0x31, 0x86},
	{0x32, 0xfb},
	{0x33, 0x60},
	//{0x2a, 0x3c},//blue screen
	{0x40, 0x08}, //MIPI page
	{0x01, 0xf0},
	{0x02, 0x01},
	{0x08, 0x0f},
	{0x20, 0x44},
	{0x34, 0xe4}, //
	{0x15, 0x0C},
#if 1
	{0x25, 0x08},
	{0x26, 0x06},
	{0x27, 0x11},
	{0x29, 0x0a},
#else //better
	{0x25, 0x0C},
	{0x26, 0x06},
	{0x27, 0x19},
	{0x29, 0x0f},
#endif
	{0x33, 0x07},
	{0x33, 0x00},
#if 1
	{0x14, 0x33},
	{0x14, 0xb3},
	{0x14, 0x33},
#else// plan B
	{0x14, 0x3b},
	{0x14, 0xbb},
	{0x14, 0x3b},
#endif
	{0x23, 0x02}, //vi test ok
	{0x23, 0x00},
};

static __maybe_unused const struct regval sensor_PAL_25fps_regs[] = {
	{0x40, 0x08}, //MIPI_PAGE
	{0x23, 0x02}, //disable mipi output
	{0x40, 0x04},
	{0xf5, 0xff},

	{0x02, 0x47},
	{0x0c, 0x13},
	{0x0d, 0x51},

	{0x15, 0x13},
	{0x16, 0x76},
	{0x17, 0x80},
	//{0x17, 0xa0},
	{0x18, 0x17},
	{0x19, 0x20},
	{0x1a, 0x17},
	//{0x1a, 0x15},
	{0x1c, 0x09},
	{0x1d, 0x48},

	{0x20, 0x48},
	{0x21, 0x84},
	{0x22, 0x37},
	{0x23, 0x3f},

	{0x2b, 0x70},
	{0x2c, 0x2a},
	{0x2d, 0x64},
	{0x2e, 0x56},

	{0x30, 0x7a},
	{0x31, 0x4a},
	{0x32, 0x4d},
	{0x33, 0xf0},

	{0x35, 0x25},
	{0x38, 0x00},
	{0x39, 0x04},

	//mipi setting
	{0x40, 0x08}, //MIPI page
	{0x01, 0xf0},
	{0x02, 0x01},
	{0x08, 0x0f},

	{0x20, 0x44},
	{0x34, 0xe4}, //
	{0x14, 0x44},
	{0x15, 0x0d},
	{0x25, 0x04},
	{0x26, 0x03},
	{0x27, 0x09},
	{0x29, 0x02},
	{0x33, 0x07},
	{0x33, 0x00},
	{0x14, 0xc4},
	{0x14, 0x44},
#if 1
	{0x04, 0x19},
	{0x21, 0x37},
#endif
	{0x23, 0x02},
	{0x23, 0x00},
};

#else
static __maybe_unused const struct regval common_setting_297M_720p_regs[] = {
	{ 0x40, 0x08 },
	{ 0x01, 0xf0 },
	{ 0x02, 0x01 },
	{ 0x08, 0x0f },
	{ 0x20, 0x44 },
	{ 0x34, 0xe4 },
	{ 0x14, 0x44 },
	{ 0x15, 0x0d },
	{ 0x25, 0x04 },
	{ 0x26, 0x03 },
	{ 0x27, 0x09 },
	{ 0x29, 0x02 },
	{ 0x33, 0x07 },
	{ 0x33, 0x00 },
	{ 0x14, 0xc4 },
	{ 0x14, 0x44 },
	// {0x23, 0x02}, //vi test ok
	// {0x23, 0x00},
};

static __maybe_unused const struct regval common_setting_594M_1080p_regs[] = {
	{ 0x40, 0x08 },
	{ 0x01, 0xf0 },
	{ 0x02, 0x01 },
	{ 0x08, 0x0f },
	{ 0x20, 0x44 },
	{ 0x34, 0xe4 },
	{ 0x15, 0x0C },
	{ 0x25, 0x08 },
	{ 0x26, 0x06 },
	{ 0x27, 0x11 },
	{ 0x29, 0x0a },
	{ 0x33, 0x07 },
	{ 0x33, 0x00 },
	{ 0x14, 0x33 },
	{ 0x14, 0xb3 },
	{ 0x14, 0x33 },
	// {0x23, 0x02}, //vi test ok
	// {0x23, 0x00},
};
#endif

static struct techpoint_video_modes supported_modes[] = {
	{
	.bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	.width = 1280,
	.height = 720,
	.max_fps = {
			.numerator = 10000,
			.denominator = 250000,
			},
	.link_freq_value = TP2855_LINK_FREQ_297M,
	.common_reg_list = common_setting_297M_720p_regs,
	.common_reg_size = ARRAY_SIZE(common_setting_297M_720p_regs),
	.bpp = 8,
	.lane = 4,
	.vc[TECHPOINT_PAD0] = 0,
	.vc[TECHPOINT_PAD1] = 1,
	.vc[TECHPOINT_PAD2] = 2,
	.vc[TECHPOINT_PAD3] = 3,
	},
	{
	.bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	.width = 720,
	.height = 576,
	.max_fps = {
			.numerator = 10000,
			.denominator = 250000,
			},
	.link_freq_value = TP2855_LINK_FREQ_297M,
	.common_reg_list = common_setting_297M_720p_regs,
	.common_reg_size = ARRAY_SIZE(common_setting_297M_720p_regs),
	.bpp = 8,
	.lane = 4,
	.vc[TECHPOINT_PAD0] = 0,
	.vc[TECHPOINT_PAD1] = 1,
	.vc[TECHPOINT_PAD2] = 2,
	.vc[TECHPOINT_PAD3] = 3,
	},
	{
	.bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	.width = 720,
	.height = 288,
	.max_fps = {
			.numerator = 10000,
			.denominator = 250000,
			},
	.link_freq_value = TP2855_LINK_FREQ_297M,
	.common_reg_list = common_setting_297M_720p_regs,
	.common_reg_size = ARRAY_SIZE(common_setting_297M_720p_regs),
	.bpp = 8,
	.lane = 4,
	.vc[TECHPOINT_PAD0] = 0,
	.vc[TECHPOINT_PAD1] = 1,
	.vc[TECHPOINT_PAD2] = 2,
	.vc[TECHPOINT_PAD3] = 3,
	},
	{
	.bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	.width = 720,
	.height = 480,
	.max_fps = {
			.numerator = 10000,
			.denominator = 250000,
			},
	.link_freq_value = TP2855_LINK_FREQ_297M,
	.common_reg_list = common_setting_297M_720p_regs,
	.common_reg_size = ARRAY_SIZE(common_setting_297M_720p_regs),
	.bpp = 8,
	.lane = 4,
	.vc[TECHPOINT_PAD0] = 0,
	.vc[TECHPOINT_PAD1] = 1,
	.vc[TECHPOINT_PAD2] = 2,
	.vc[TECHPOINT_PAD3] = 3,
	},
	{
	.bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	.width = 720,
	.height = 240,
	.max_fps = {
			.numerator = 10000,
			.denominator = 250000,
			},
	.link_freq_value = TP2855_LINK_FREQ_297M,
	.common_reg_list = common_setting_297M_720p_regs,
	.common_reg_size = ARRAY_SIZE(common_setting_297M_720p_regs),
	.bpp = 8,
	.lane = 4,
	.vc[TECHPOINT_PAD0] = 0,
	.vc[TECHPOINT_PAD1] = 1,
	.vc[TECHPOINT_PAD2] = 2,
	.vc[TECHPOINT_PAD3] = 3,
	},
	{
	 .bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	 .width = 1920,
	 .height = 1080,
	 .max_fps = {
		     .numerator = 10000,
		     .denominator = 250000,
		     },
	 .link_freq_value = TP2855_LINK_FREQ_594M,
	 .common_reg_list = common_setting_594M_1080p_regs,
	 .common_reg_size = ARRAY_SIZE(common_setting_594M_1080p_regs),
	 .bpp = 8,
	 .lane = 4,
	 .vc[TECHPOINT_PAD0] = 0,
	 .vc[TECHPOINT_PAD1] = 1,
	 .vc[TECHPOINT_PAD2] = 2,
	 .vc[TECHPOINT_PAD3] = 3,
	},
	{
	.bus_fmt = MEDIA_BUS_FMT_UYVY8_2X8,
	.width = 1920,
	.height = 576,
	.max_fps = {
			.numerator = 10000,
			.denominator = 250000,
			},
	.link_freq_value = TP2855_LINK_FREQ_297M,
	.common_reg_list = sensor_PAL_25fps_regs,
	.common_reg_size = ARRAY_SIZE(sensor_PAL_25fps_regs),
	.bpp = 8,
	.lane = 4,
	.vc[TECHPOINT_PAD0] = 0,
	.vc[TECHPOINT_PAD1] = 1,
	.vc[TECHPOINT_PAD2] = 2,
	.vc[TECHPOINT_PAD3] = 3,
	},
};


enum{
    CH_1=0,   //
    CH_2=1,   //
    CH_3=2,   //
    CH_4=3,   //   
    CH_ALL=4,   //     
    MIPI_PAGE=8,
};
enum{
    STD_TVI, //TVI
    STD_HDA, //AHD
};
enum{
    PAL,
    NTSC,
    HD25,  //720p25
    HD30,  //720p30
    FHD25, //1080p25
    FHD30, //1080p30
    FHD50, //1080p50
    FHD60, //1080p60   
	PAL1920,
};
void TP2855_decoder_init(unsigned char ch,unsigned char fmt,unsigned char std);


int tp2855_get_reso(struct i2c_client *client)
{
	u8 detect_fmt = 0xff;
	u8 reso = 0xff;

	u8 ch = 0;
	u8 res[TP2855_CVSTD_MAX] = {0};
	int i = 0;
	reso = TP2855_CVSTD_720P_25;
	while(1)
	{
		techpoint_write_reg(client, 0x40, ch%4);
		techpoint_read_reg(client, 0x03, &detect_fmt);
		reso = detect_fmt & 0x7;
		switch (reso) {
			case TP2855_CVSTD_1080P_30:
			{
				dev_err(&client->dev, "detect channel %d 1080P_30\n", ch%4);
				res[reso]++;
				if(res[reso] >= 3)
					return reso;
				break;
			}
			case TP2855_CVSTD_1080P_25:
			{
				dev_err(&client->dev, "detect channel %d 1080P_25\n", ch%4);
				res[reso]++;
				if(res[reso] >= 3)
					return reso;
				break;
			}
			case TP2855_CVSTD_720P_30:
			{
				dev_err(&client->dev, "detect channel %d 720P_30\n", ch%4);
				res[reso]++;
				if(res[reso] >= 3)
					return reso;
				break;
			}
			case TP2855_CVSTD_720P_25:
			{
				dev_err(&client->dev, "detect channel %d 720P_25\n", ch%4);
				res[reso]++;
				if(res[reso] >= 3)
					return reso;
				break;
			}
			case TP2855_CVSTD_SD:
			{
				dev_err(&client->dev, "detect channel %d PAL\n", ch%4);
				res[reso]++;
				if(res[reso] >= 3)
				{
					TP2855_decoder_init(CH_ALL, PAL, STD_HDA);
					techpoint_write_reg(client, 0x40, ch%4);
					for(i=0; i<20; i++)
					{
						usleep_range(50000, 51000);
						techpoint_read_reg(client, 0x01, &detect_fmt);
						dev_err(&client->dev, "detect channel %d PAL detect_fmt = 0x%02x\n", ch%4, detect_fmt);
						if(detect_fmt == 0x7c)
							return reso;
					}
					dev_err(&client->dev, "detect channel %d NTSC\n", ch%4);
					return TP2855_CVSTD_SD_NTSC;
				}
				break;
			}
			default:
			{
				dev_err(&client->dev, "detect channel %d UNSUPPORT, reso = %d\n", ch%4, reso);
				break;
			}
		}
		ch++;
		if(ch >= 40)
			break;
		usleep_range(50000, 51000);
	}

	return reso;
}

int tp2855_initialize(struct techpoint *techpoint)
{
	int array_size = 0;
	struct i2c_client *client = techpoint->client;
	struct device *dev = &client->dev;
	u8 reso = 0xff;

	techpoint->video_modes_num = ARRAY_SIZE(supported_modes);
	array_size =
	    sizeof(struct techpoint_video_modes) * techpoint->video_modes_num;
	techpoint->video_modes = devm_kzalloc(dev, array_size, GFP_KERNEL);
	memcpy(techpoint->video_modes, supported_modes, array_size);


	reso = tp2855_get_reso(client);
	if(reso == TP2855_CVSTD_1080P_25)
	{
		techpoint->cur_video_mode = &techpoint->video_modes[5];
	}
	else if(reso == TP2855_CVSTD_SD)
	{
		techpoint->cur_video_mode = &techpoint->video_modes[1];
	}
	else if(reso == TP2855_CVSTD_SD_NTSC)
	{
		techpoint->cur_video_mode = &techpoint->video_modes[3];
	}
	else
	{
		techpoint->cur_video_mode = &techpoint->video_modes[0];
	}

	return 0;
}

int tp2855_get_channel_input_status(struct i2c_client *client, u8 ch)
{
	u8 val = 0;

	techpoint_write_reg(client, PAGE_REG, ch);
	techpoint_read_reg(client, INPUT_STATUS_REG, &val);
	dev_dbg(&client->dev, "input_status ch %d : %x\n", ch, val);

	return (val & INPUT_STATUS_MASK) ? 0 : 1;
}

int tp2855_get_all_input_status(struct i2c_client *client, u8 *detect_status)
{
	u8 val = 0, i;

	for (i = 0; i < PAD_MAX; i++) {
		techpoint_write_reg(client, PAGE_REG, i);
		techpoint_read_reg(client, INPUT_STATUS_REG, &val);
		detect_status[i] = tp2855_get_channel_input_status(client, i);
	}

	return 0;
}

int tp2855_set_channel_reso(struct i2c_client *client, int ch,
			    enum techpoint_support_reso reso)
{
	int val = reso;
	u8 tmp;
	const unsigned char SYS_MODE[5] = { 0x01, 0x02, 0x04, 0x08, 0x0f };

	techpoint_write_reg(client, 0x40, ch);

	switch (val) {
	case TECHPOINT_S_RESO_1080P_30:
		dev_err(&client->dev, "set channel %d 1080P_30, TBD\n", ch);
		techpoint_read_reg(client, 0xf5, &tmp);
		tmp &= ~SYS_MODE[ch];
		techpoint_write_reg(client, 0xf5, tmp);
		techpoint_write_reg(client, 0x02, 0x40);
		techpoint_write_reg(client, 0x07, 0xc0);
		techpoint_write_reg(client, 0x0b, 0xc0);
		techpoint_write_reg(client, 0x0c, 0x03);
		techpoint_write_reg(client, 0x0d, 0x50);
		techpoint_write_reg(client, 0x15, 0x03);
		techpoint_write_reg(client, 0x16, 0xd2);
		techpoint_write_reg(client, 0x17, 0x80);
		techpoint_write_reg(client, 0x18, 0x29);
		techpoint_write_reg(client, 0x19, 0x38);
		techpoint_write_reg(client, 0x1a, 0x47);
		techpoint_write_reg(client, 0x1c, 0x08);
		techpoint_write_reg(client, 0x1d, 0x98);
		techpoint_write_reg(client, 0x20, 0x30);
		techpoint_write_reg(client, 0x21, 0x84);
		techpoint_write_reg(client, 0x22, 0x36);
		techpoint_write_reg(client, 0x23, 0x3c);
		techpoint_write_reg(client, 0x2b, 0x60);
		techpoint_write_reg(client, 0x2c, 0x0a);
		techpoint_write_reg(client, 0x2d, 0x30);
		techpoint_write_reg(client, 0x2e, 0x70);
		techpoint_write_reg(client, 0x30, 0x48);
		techpoint_write_reg(client, 0x31, 0xbb);
		techpoint_write_reg(client, 0x32, 0x2e);
		techpoint_write_reg(client, 0x33, 0x90);
		techpoint_write_reg(client, 0x35, 0x05);
		techpoint_write_reg(client, 0x38, 0x00);
		techpoint_write_reg(client, 0x39, 0x1C);
		//def ahd config
		techpoint_write_reg(client, 0x02, 0x44);
		techpoint_write_reg(client, 0x0d, 0x72);
		techpoint_write_reg(client, 0x15, 0x01);
		techpoint_write_reg(client, 0x16, 0xf0);
		techpoint_write_reg(client, 0x20, 0x38);
		techpoint_write_reg(client, 0x21, 0x46);
		techpoint_write_reg(client, 0x25, 0xfe);
		techpoint_write_reg(client, 0x26, 0x0d);
		techpoint_write_reg(client, 0x2c, 0x3a);
		techpoint_write_reg(client, 0x2d, 0x54);
		techpoint_write_reg(client, 0x2e, 0x40);
		techpoint_write_reg(client, 0x30, 0xa5);
		techpoint_write_reg(client, 0x31, 0x95);
		techpoint_write_reg(client, 0x32, 0xe0);
		techpoint_write_reg(client, 0x33, 0x60);
		break;
	case TECHPOINT_S_RESO_1080P_25:
		dev_err(&client->dev, "set channel %d 1080P_25\n", ch);
		techpoint_read_reg(client, 0xf5, &tmp);
		tmp &= ~SYS_MODE[ch];
		techpoint_write_reg(client, 0xf5, tmp);
		techpoint_write_reg(client, 0x02, 0x40);
		techpoint_write_reg(client, 0x07, 0xc0);
		techpoint_write_reg(client, 0x0b, 0xc0);
		techpoint_write_reg(client, 0x0c, 0x03);
		techpoint_write_reg(client, 0x0d, 0x50);
		techpoint_write_reg(client, 0x15, 0x03);
		techpoint_write_reg(client, 0x16, 0xd2);
		techpoint_write_reg(client, 0x17, 0x80);
		techpoint_write_reg(client, 0x18, 0x29);
		techpoint_write_reg(client, 0x19, 0x38);
		techpoint_write_reg(client, 0x1a, 0x47);
		techpoint_write_reg(client, 0x1c, 0x0a);
		techpoint_write_reg(client, 0x1d, 0x50);
		techpoint_write_reg(client, 0x20, 0x30);
		techpoint_write_reg(client, 0x21, 0x84);
		techpoint_write_reg(client, 0x22, 0x36);
		techpoint_write_reg(client, 0x23, 0x3c);
		techpoint_write_reg(client, 0x2b, 0x60);
		techpoint_write_reg(client, 0x2c, 0x0a);
		techpoint_write_reg(client, 0x2d, 0x30);
		techpoint_write_reg(client, 0x2e, 0x70);
		techpoint_write_reg(client, 0x30, 0x48);
		techpoint_write_reg(client, 0x31, 0xbb);
		techpoint_write_reg(client, 0x32, 0x2e);
		techpoint_write_reg(client, 0x33, 0x90);
		techpoint_write_reg(client, 0x35, 0x05);
		techpoint_write_reg(client, 0x38, 0x00);
		techpoint_write_reg(client, 0x39, 0x1C);
		//def ahd config
		techpoint_write_reg(client, 0x02, 0x44);
		techpoint_write_reg(client, 0x0d, 0x73);
		techpoint_write_reg(client, 0x15, 0x01);
		techpoint_write_reg(client, 0x16, 0xf0);
		techpoint_write_reg(client, 0x20, 0x3c);
		techpoint_write_reg(client, 0x21, 0x46);
		techpoint_write_reg(client, 0x25, 0xfe);
		techpoint_write_reg(client, 0x26, 0x0d);
		techpoint_write_reg(client, 0x2c, 0x3a);
		techpoint_write_reg(client, 0x2d, 0x54);
		techpoint_write_reg(client, 0x2e, 0x40);
		techpoint_write_reg(client, 0x30, 0xa5);
		techpoint_write_reg(client, 0x31, 0x86);
		techpoint_write_reg(client, 0x32, 0xfb);
		techpoint_write_reg(client, 0x33, 0x60);
		break;
	case TECHPOINT_S_RESO_720P_30:
		dev_err(&client->dev, "set channel %d 720P_30\n", ch);
		techpoint_read_reg(client, 0xf5, &tmp);
		tmp |= SYS_MODE[ch];
		techpoint_write_reg(client, 0xf5, tmp);
		techpoint_write_reg(client, 0x02, 0x42);
		techpoint_write_reg(client, 0x07, 0xc0);
		techpoint_write_reg(client, 0x0b, 0xc0);
		techpoint_write_reg(client, 0x0c, 0x13);
		techpoint_write_reg(client, 0x0d, 0x50);
		techpoint_write_reg(client, 0x15, 0x13);
		techpoint_write_reg(client, 0x16, 0x15);
		techpoint_write_reg(client, 0x17, 0x00);
		techpoint_write_reg(client, 0x18, 0x19);
		techpoint_write_reg(client, 0x19, 0xd0);
		techpoint_write_reg(client, 0x1a, 0x25);
		techpoint_write_reg(client, 0x1c, 0x06);
		techpoint_write_reg(client, 0x1d, 0x72);
		techpoint_write_reg(client, 0x20, 0x30);
		techpoint_write_reg(client, 0x21, 0x84);
		techpoint_write_reg(client, 0x22, 0x36);
		techpoint_write_reg(client, 0x23, 0x3c);
		techpoint_write_reg(client, 0x2b, 0x60);
		techpoint_write_reg(client, 0x2c, 0x0a);
		techpoint_write_reg(client, 0x2d, 0x30);
		techpoint_write_reg(client, 0x2e, 0x70);
		techpoint_write_reg(client, 0x30, 0x48);
		techpoint_write_reg(client, 0x31, 0xbb);
		techpoint_write_reg(client, 0x32, 0x2e);
		techpoint_write_reg(client, 0x33, 0x90);
		techpoint_write_reg(client, 0x35, 0x25);
		techpoint_write_reg(client, 0x38, 0x00);
		techpoint_write_reg(client, 0x39, 0x18);
		//def ahd config
		techpoint_write_reg(client, 0x02, 0x46);
		techpoint_write_reg(client, 0x0d, 0x70);
		techpoint_write_reg(client, 0x20, 0x40);
		techpoint_write_reg(client, 0x21, 0x46);
		techpoint_write_reg(client, 0x25, 0xfe);
		techpoint_write_reg(client, 0x26, 0x01);
		techpoint_write_reg(client, 0x2c, 0x3a);
		techpoint_write_reg(client, 0x2d, 0x5a);
		techpoint_write_reg(client, 0x2e, 0x40);
		techpoint_write_reg(client, 0x30, 0x9d);
		techpoint_write_reg(client, 0x31, 0xca);
		techpoint_write_reg(client, 0x32, 0x01);
		techpoint_write_reg(client, 0x33, 0xd0);
		break;
	case TECHPOINT_S_RESO_720P_25:
		dev_err(&client->dev, "set channel %d 720P_25\n", ch);
		techpoint_read_reg(client, 0xf5, &tmp);
		tmp |= SYS_MODE[ch];
		techpoint_write_reg(client, 0xf5, tmp);
		techpoint_write_reg(client, 0x02, 0x42);
		techpoint_write_reg(client, 0x07, 0xc0);
		techpoint_write_reg(client, 0x0b, 0xc0);
		techpoint_write_reg(client, 0x0c, 0x13);
		techpoint_write_reg(client, 0x0d, 0x50);
		techpoint_write_reg(client, 0x15, 0x13);
		techpoint_write_reg(client, 0x16, 0x15);
		techpoint_write_reg(client, 0x17, 0x00);
		techpoint_write_reg(client, 0x18, 0x19);
		techpoint_write_reg(client, 0x19, 0xd0);
		techpoint_write_reg(client, 0x1a, 0x25);
		techpoint_write_reg(client, 0x1c, 0x07);
		techpoint_write_reg(client, 0x1d, 0xbc);
		techpoint_write_reg(client, 0x20, 0x30);
		techpoint_write_reg(client, 0x21, 0x84);
		techpoint_write_reg(client, 0x22, 0x36);
		techpoint_write_reg(client, 0x23, 0x3c);
		techpoint_write_reg(client, 0x2b, 0x60);
		techpoint_write_reg(client, 0x2c, 0x0a);
		techpoint_write_reg(client, 0x2d, 0x30);
		techpoint_write_reg(client, 0x2e, 0x70);
		techpoint_write_reg(client, 0x30, 0x48);
		techpoint_write_reg(client, 0x31, 0xbb);
		techpoint_write_reg(client, 0x32, 0x2e);
		techpoint_write_reg(client, 0x33, 0x90);
		techpoint_write_reg(client, 0x35, 0x25);
		techpoint_write_reg(client, 0x38, 0x00);
		techpoint_write_reg(client, 0x39, 0x18);
		//def ahd config
		techpoint_write_reg(client, 0x02, 0x46);
		techpoint_write_reg(client, 0x0d, 0x71);
		techpoint_write_reg(client, 0x20, 0x40);
		techpoint_write_reg(client, 0x21, 0x46);
		techpoint_write_reg(client, 0x25, 0xfe);
		techpoint_write_reg(client, 0x26, 0x01);
		techpoint_write_reg(client, 0x2c, 0x3a);
		techpoint_write_reg(client, 0x2d, 0x5a);
		techpoint_write_reg(client, 0x2e, 0x40);
		techpoint_write_reg(client, 0x30, 0x9e);
		techpoint_write_reg(client, 0x31, 0x20);
		techpoint_write_reg(client, 0x32, 0x10);
		techpoint_write_reg(client, 0x33, 0x90);
		break;
	default:
		dev_err(&client->dev,
			"set channel %d UNSUPPORT, default 1080P_25\n", ch);
		techpoint_read_reg(client, 0xf5, &tmp);
		tmp &= ~SYS_MODE[ch];
		techpoint_write_reg(client, 0xf5, tmp);
		techpoint_write_reg(client, 0x02, 0x40);
		techpoint_write_reg(client, 0x07, 0xc0);
		techpoint_write_reg(client, 0x0b, 0xc0);
		techpoint_write_reg(client, 0x0c, 0x03);
		techpoint_write_reg(client, 0x0d, 0x50);
		techpoint_write_reg(client, 0x15, 0x03);
		techpoint_write_reg(client, 0x16, 0xd2);
		techpoint_write_reg(client, 0x17, 0x80);
		techpoint_write_reg(client, 0x18, 0x29);
		techpoint_write_reg(client, 0x19, 0x38);
		techpoint_write_reg(client, 0x1a, 0x47);
		techpoint_write_reg(client, 0x1c, 0x0a);
		techpoint_write_reg(client, 0x1d, 0x50);
		techpoint_write_reg(client, 0x20, 0x30);
		techpoint_write_reg(client, 0x21, 0x84);
		techpoint_write_reg(client, 0x22, 0x36);
		techpoint_write_reg(client, 0x23, 0x3c);
		techpoint_write_reg(client, 0x2b, 0x60);
		techpoint_write_reg(client, 0x2c, 0x0a);
		techpoint_write_reg(client, 0x2d, 0x30);
		techpoint_write_reg(client, 0x2e, 0x70);
		techpoint_write_reg(client, 0x30, 0x48);
		techpoint_write_reg(client, 0x31, 0xbb);
		techpoint_write_reg(client, 0x32, 0x2e);
		techpoint_write_reg(client, 0x33, 0x90);
		techpoint_write_reg(client, 0x35, 0x05);
		techpoint_write_reg(client, 0x38, 0x00);
		techpoint_write_reg(client, 0x39, 0x1C);
		//def ahd config
		techpoint_write_reg(client, 0x02, 0x44);
		techpoint_write_reg(client, 0x0d, 0x73);
		techpoint_write_reg(client, 0x15, 0x01);
		techpoint_write_reg(client, 0x16, 0xf0);
		techpoint_write_reg(client, 0x20, 0x3c);
		techpoint_write_reg(client, 0x21, 0x46);
		techpoint_write_reg(client, 0x25, 0xfe);
		techpoint_write_reg(client, 0x26, 0x0d);
		techpoint_write_reg(client, 0x2c, 0x3a);
		techpoint_write_reg(client, 0x2d, 0x54);
		techpoint_write_reg(client, 0x2e, 0x40);
		techpoint_write_reg(client, 0x30, 0xa5);
		techpoint_write_reg(client, 0x31, 0x86);
		techpoint_write_reg(client, 0x32, 0xfb);
		techpoint_write_reg(client, 0x33, 0x60);
		break;
	}

#if TECHPOINT_TEST_PATTERN
	techpoint_write_reg(client, 0x2a, 0x3c);
#endif

	return 0;
}

int tp2855_get_channel_reso(struct i2c_client *client, int ch)
{
	u8 detect_fmt = 0xff;
	u8 reso = 0xff;

	techpoint_write_reg(client, 0x40, ch);
	techpoint_read_reg(client, 0x03, &detect_fmt);
	reso = detect_fmt & 0x7;

	switch (reso) {
	case TP2855_CVSTD_1080P_30:
		dev_err(&client->dev, "detect channel %d 1080P_30\n", ch);
		return TECHPOINT_S_RESO_1080P_30;
		break;
	case TP2855_CVSTD_1080P_25:
		dev_err(&client->dev, "detect channel %d 1080P_25\n", ch);
		return TECHPOINT_S_RESO_1080P_25;
		break;
	case TP2855_CVSTD_720P_30:
		dev_err(&client->dev, "detect channel %d 720P_30\n", ch);
		return TECHPOINT_S_RESO_720P_30;
		break;
	case TP2855_CVSTD_720P_25:
		dev_err(&client->dev, "detect channel %d 720P_25\n", ch);
		return TECHPOINT_S_RESO_720P_25;
		break;
	default:
		dev_err(&client->dev,
			"detect channel %d UNSUPPORT, default 1080P_25\n", ch);
		return TECHPOINT_S_RESO_1080P_25;
		break;
	}

	return reso;
}

int tp2855_set_quick_stream(struct i2c_client *client, u32 stream)
{
	pr_alert("tp2855_set_quick_stream stream = %d\n", stream);
	if (stream) {
		techpoint_write_reg(client, 0x40, 0x8);
		techpoint_write_reg(client, 0x23, 0x0);
	} else {
		techpoint_write_reg(client, 0x40, 0x8);
		techpoint_write_reg(client, 0x23, 0x2);
	}

	return 0;
}
