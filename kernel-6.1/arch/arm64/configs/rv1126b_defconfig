# CONFIG_LOCALVERSION_AUTO is not set
CONFIG_DEFAULT_HOSTNAME="localhost"
CONFIG_SYSVIPC=y
CONFIG_NO_HZ_IDLE=y
CONFIG_HIGH_RES_TIMERS=y
CONFIG_LOG_BUF_SHIFT=18
CONFIG_CGROUPS=y
CONFIG_EXT4_FS=y
CONFIG_BLK_DEV_INITRD=y
# CONFIG_RD_BZIP2 is not set
# CONFIG_RD_LZMA is not set
# CONFIG_RD_XZ is not set
# CONFIG_RD_LZO is not set
# CONFIG_RD_LZ4 is not set
# CONFIG_RD_ZSTD is not set
CONFIG_CC_OPTIMIZE_FOR_SIZE=y
# CONFIG_ELF_CORE is not set
# CONFIG_BASE_FULL is not set
# CONFIG_IO_URING is not set
CONFIG_EMBEDDED=y
CONFIG_PERF_EVENTS=y
CONFIG_ARCH_ROCKCHIP=y

# CONFIG_AMPERE_ERRATUM_AC03_CPU_38 is not set
# CONFIG_ARM64_ERRATUM_826319 is not set
# CONFIG_ARM64_ERRATUM_827319 is not set
# CONFIG_ARM64_ERRATUM_824069 is not set
# CONFIG_ARM64_ERRATUM_819472 is not set
# CONFIG_ARM64_ERRATUM_832075 is not set
# CONFIG_ARM64_ERRATUM_1742098 is not set
# CONFIG_ARM64_ERRATUM_1024718 is not set
# CONFIG_ARM64_ERRATUM_1418040 is not set
# CONFIG_ARM64_ERRATUM_1165522 is not set
# CONFIG_ARM64_ERRATUM_1319367 is not set
# CONFIG_ARM64_ERRATUM_1530923 is not set
# CONFIG_ARM64_ERRATUM_2441007 is not set
# CONFIG_ARM64_ERRATUM_1286807 is not set
# CONFIG_ARM64_ERRATUM_1463225 is not set
# CONFIG_ARM64_ERRATUM_1542419 is not set
# CONFIG_ARM64_ERRATUM_1508412 is not set
# CONFIG_ARM64_ERRATUM_2051678 is not set
# CONFIG_ARM64_ERRATUM_2077057 is not set
# CONFIG_ARM64_ERRATUM_2658417 is not set
# CONFIG_ARM64_ERRATUM_2054223 is not set
# CONFIG_ARM64_ERRATUM_2067961 is not set
# CONFIG_ARM64_ERRATUM_2441009 is not set
# CONFIG_ARM64_ERRATUM_2966298 is not set
# CONFIG_CAVIUM_ERRATUM_22375 is not set
# CONFIG_CAVIUM_ERRATUM_23154 is not set
# CONFIG_CAVIUM_ERRATUM_27456 is not set
# CONFIG_CAVIUM_ERRATUM_30115 is not set
# CONFIG_CAVIUM_TX2_ERRATUM_219 is not set
# CONFIG_FUJITSU_ERRATUM_010001 is not set
# CONFIG_HISILICON_ERRATUM_161600802 is not set
# CONFIG_QCOM_FALKOR_ERRATUM_1003 is not set
# CONFIG_QCOM_FALKOR_ERRATUM_1009 is not set
# CONFIG_QCOM_QDF2400_ERRATUM_0065 is not set
# CONFIG_QCOM_FALKOR_ERRATUM_E1041 is not set
# CONFIG_NVIDIA_CARMEL_CNP_ERRATUM is not set
# CONFIG_SOCIONEXT_SYNQUACER_PREITS is not set
CONFIG_SCHED_MC=y
CONFIG_NR_CPUS=4
# CONFIG_UNMAP_KERNEL_AT_EL0 is not set
# CONFIG_MITIGATE_SPECTRE_BRANCH_HISTORY is not set
CONFIG_COMPAT=y
CONFIG_REALTEK_PHY=y
CONFIG_MICREL_PHY=y
CONFIG_ROCKCHIP_PHY=y
CONFIG_MDIO_BUS=y

# CONFIG_ARM64_HW_AFDBM is not set
# CONFIG_ARM64_PAN is not set
# CONFIG_ARM64_USE_LSE_ATOMICS is not set
# CONFIG_ARM64_RAS_EXTN is not set
# CONFIG_ARM64_CNP is not set
# CONFIG_ARM64_PTR_AUTH is not set
# CONFIG_ARM64_AMU_EXTN is not set
# CONFIG_ARM64_TLB_RANGE is not set
# CONFIG_ARM64_BTI is not set
# CONFIG_ARM64_E0PD is not set
# CONFIG_ARM64_SVE is not set
CONFIG_ARM64_PSEUDO_NMI=y
# CONFIG_EFI is not set
CONFIG_ENERGY_MODEL=y
CONFIG_CPU_IDLE=y
CONFIG_ARM_PSCI_CPUIDLE=y
CONFIG_CPU_FREQ=y
CONFIG_CPU_FREQ_DEFAULT_GOV_INTERACTIVE=y
CONFIG_CPU_FREQ_GOV_USERSPACE=y
CONFIG_CPU_FREQ_GOV_ONDEMAND=y
CONFIG_CPUFREQ_DT=y
CONFIG_ARM_ROCKCHIP_CPUFREQ=y
CONFIG_JUMP_LABEL=y
# CONFIG_SECCOMP is not set
# CONFIG_STACKPROTECTOR_STRONG is not set
CONFIG_MODULES=y
CONFIG_MODULE_UNLOAD=y
# CONFIG_BLK_DEBUG_FS is not set
CONFIG_PARTITION_ADVANCED=y
CONFIG_CMDLINE_PARTITION=y
CONFIG_IOSCHED_BFQ=y
# CONFIG_SLUB_SYSFS is not set
# CONFIG_COMPAT_BRK is not set
# CONFIG_COMPACTION is not set
CONFIG_DEFAULT_MMAP_MIN_ADDR=32768
CONFIG_CMA=y
# CONFIG_ZONE_DMA is not set
# CONFIG_ZONE_DMA32 is not set
# CONFIG_VM_EVENT_COUNTERS is not set
CONFIG_LRU_GEN=y
CONFIG_LRU_GEN_ENABLED=y
CONFIG_NET=y
CONFIG_PACKET=y
CONFIG_UNIX=y
CONFIG_INET=y
# CONFIG_INET_DIAG is not set
# CONFIG_IPV6 is not set
CONFIG_BT=y
CONFIG_BT_RFCOMM=y
CONFIG_BT_RFCOMM_TTY=y
CONFIG_BT_HS=y
CONFIG_BT_HCIUART=y
CONFIG_BT_HCIUART_H4=y
CONFIG_CFG80211_WEXT=y
CONFIG_RFKILL=y
CONFIG_RFKILL_RK=y
CONFIG_DEVTMPFS=y
CONFIG_DEVTMPFS_MOUNT=y
# CONFIG_ALLOW_DEV_COREDUMP is not set
CONFIG_ARM_SCMI_PROTOCOL=y
CONFIG_ROCKCHIP_SIP=y
CONFIG_SRAM=y
CONFIG_NETDEVICES=y
CONFIG_STMMAC_ETH=y
# CONFIG_DWMAC_GENERIC is not set
CONFIG_ROCKCHIP_FEPHY=y
# CONFIG_USB_NET_DRIVERS is not set
# CONFIG_WLAN_VENDOR_ADMTEK is not set
# CONFIG_WLAN_VENDOR_ATH is not set
# CONFIG_WLAN_VENDOR_ATMEL is not set
# CONFIG_WLAN_VENDOR_BROADCOM is not set
# CONFIG_WLAN_VENDOR_CISCO is not set
# CONFIG_WLAN_VENDOR_INTEL is not set
# CONFIG_WLAN_VENDOR_INTERSIL is not set
# CONFIG_WLAN_VENDOR_MARVELL is not set
# CONFIG_WLAN_VENDOR_MEDIATEK is not set
# CONFIG_WLAN_VENDOR_MICROCHIP is not set
# CONFIG_WLAN_VENDOR_PURELIFI is not set
# CONFIG_WLAN_VENDOR_RALINK is not set
# CONFIG_WLAN_VENDOR_REALTEK is not set
# CONFIG_WLAN_VENDOR_RSI is not set
# CONFIG_WLAN_VENDOR_SILABS is not set
# CONFIG_WLAN_VENDOR_ST is not set
# CONFIG_WLAN_VENDOR_TI is not set
# CONFIG_WLAN_VENDOR_ZYDAS is not set
# CONFIG_WLAN_VENDOR_QUANTENNA is not set
CONFIG_WL_ROCKCHIP=y
CONFIG_WIFI_BUILD_MODULE=y
CONFIG_AP6XXX=m
CONFIG_INPUT_EVDEV=y
CONFIG_KEYBOARD_ADC=y
# CONFIG_KEYBOARD_ATKBD is not set
CONFIG_KEYBOARD_GPIO=y
# CONFIG_INPUT_MOUSE is not set
CONFIG_INPUT_TOUCHSCREEN=y
CONFIG_TOUCHSCREEN_GT1X=y
CONFIG_INPUT_MISC=y
CONFIG_INPUT_RK805_PWRKEY=y
# CONFIG_SERIO is not set
# CONFIG_CONSOLE_TRANSLATIONS is not set
# CONFIG_LEGACY_PTYS is not set
CONFIG_SERIAL_8250=y
# CONFIG_SERIAL_8250_DEPRECATED_OPTIONS is not set
# CONFIG_SERIAL_8250_16550A_VARIANTS is not set
CONFIG_SERIAL_8250_CONSOLE=y
CONFIG_SERIAL_8250_NR_UARTS=8
CONFIG_SERIAL_8250_RUNTIME_UARTS=8
CONFIG_SERIAL_8250_DW=y
CONFIG_HW_RANDOM=y
CONFIG_HW_RANDOM_ROCKCHIP=y
CONFIG_I2C_CHARDEV=y
CONFIG_I2C_RK3X=y
CONFIG_SPI=y
CONFIG_SPI_ROCKCHIP=y
CONFIG_SPI_ROCKCHIP_SFC=y
CONFIG_PINCTRL_RK805=y
CONFIG_GPIO_SYSFS=y
CONFIG_SYSCON_REBOOT_MODE=y
CONFIG_BATTERY_RK817=y
CONFIG_CHARGER_RK817=y
# CONFIG_HWMON is not set
CONFIG_THERMAL=y
CONFIG_THERMAL_WRITABLE_TRIPS=y
CONFIG_CPU_THERMAL=y
CONFIG_DEVFREQ_THERMAL=y
CONFIG_ROCKCHIP_THERMAL=y
CONFIG_WATCHDOG=y
CONFIG_DW_WATCHDOG=y
CONFIG_MFD_RK808=y
CONFIG_REGULATOR=y
CONFIG_REGULATOR_FIXED_VOLTAGE=y
CONFIG_REGULATOR_GPIO=y
CONFIG_REGULATOR_PWM=y
CONFIG_REGULATOR_RK801=y
CONFIG_REGULATOR_RK808=y
CONFIG_MEDIA_SUPPORT=y
CONFIG_MEDIA_SUPPORT_FILTER=y
# CONFIG_MEDIA_SUBDRV_AUTOSELECT is not set
CONFIG_MEDIA_CAMERA_SUPPORT=y
CONFIG_MEDIA_PLATFORM_SUPPORT=y
CONFIG_MEDIA_USB_SUPPORT=y
CONFIG_USB_VIDEO_CLASS=y
# CONFIG_USB_VIDEO_CLASS_INPUT_EVDEV is not set
CONFIG_V4L_PLATFORM_DRIVERS=y
CONFIG_V4L_MEM2MEM_DRIVERS=y
CONFIG_VIDEO_ROCKCHIP_CIF=y
CONFIG_VIDEO_ROCKCHIP_ISP=y
CONFIG_VIDEO_ROCKCHIP_VPSS=y
CONFIG_VIDEO_IMX415=y
CONFIG_VIDEO_TECHPOINT=y
CONFIG_VIDEO_TP2855=y
CONFIG_MRV220_CAMERA_POWER_CRT=y
CONFIG_VIDEO_IMX415=y
CONFIG_VIDEO_OS04A10=y
CONFIG_VIDEO_SC200AI=y
CONFIG_VIDEO_SC450AI=y
CONFIG_VIDEO_SC850SL=y
CONFIG_DRM=y
CONFIG_DRM_IGNORE_IOTCL_PERMIT=y
CONFIG_DRM_ROCKCHIP=y
CONFIG_ROCKCHIP_VOP=y
CONFIG_ROCKCHIP_DW_MIPI_DSI=y
CONFIG_ROCKCHIP_RGB=y
CONFIG_DRM_PANEL_SIMPLE=y
CONFIG_BACKLIGHT_CLASS_DEVICE=y
CONFIG_BACKLIGHT_PWM=y
CONFIG_ROCKCHIP_MULTI_RGA=y
CONFIG_ROCKCHIP_RGA_PROC_FS=y
# CONFIG_ROCKCHIP_RGA_DEBUG_FS is not set
CONFIG_ROCKCHIP_MPP_OSAL=y
CONFIG_SOUND=y
CONFIG_SND=y
# CONFIG_SND_PCM_TIMER is not set
# CONFIG_SND_SUPPORT_OLD_API is not set
# CONFIG_SND_PROC_FS is not set
# CONFIG_SND_CTL_FAST_LOOKUP is not set
# CONFIG_SND_DRIVERS is not set
# CONFIG_SND_USB is not set
CONFIG_SND_SOC=y
CONFIG_SND_SOC_ROCKCHIP=y
CONFIG_SND_SOC_ROCKCHIP_ASRC=y
CONFIG_SND_SOC_ROCKCHIP_PDM_V2=y
CONFIG_SND_SOC_ROCKCHIP_SAI=y
CONFIG_SND_SOC_ROCKCHIP_MULTICODECS=y
CONFIG_SND_SOC_DUMMY_CODEC=y
CONFIG_SND_SOC_RK3506=y
CONFIG_SND_SOC_RK817=y
CONFIG_SND_SOC_RK_DSM=y
CONFIG_SND_SIMPLE_CARD=y
CONFIG_USB=y
CONFIG_USB_ANNOUNCE_NEW_DEVICES=y
CONFIG_USB_OTG=y
CONFIG_USB_XHCI_HCD=y
CONFIG_USB_EHCI_HCD=y
CONFIG_USB_EHCI_HCD_PLATFORM=y
CONFIG_USB_OHCI_HCD=y
CONFIG_USB_OHCI_HCD_PLATFORM=y
CONFIG_USB_DWC3=y
CONFIG_USB_GADGET=y
CONFIG_USB_GADGET_DEBUG_FILES=y
CONFIG_USB_GADGET_VBUS_DRAW=500
CONFIG_USB_CONFIGFS=y
CONFIG_USB_CONFIGFS_UEVENT=y
CONFIG_USB_CONFIGFS_F_FS=y
CONFIG_TYPEC=y
CONFIG_TYPEC_TCPM=y
CONFIG_TYPEC_TCPCI=y
CONFIG_TYPEC_HUSB311=y
CONFIG_TYPEC_FUSB302=y
CONFIG_TYPEC_DP_ALTMODE=y
CONFIG_MMC=y
# CONFIG_PWRSEQ_EMMC is not set
CONFIG_MMC_BLOCK_MINORS=32
CONFIG_MMC_QUEUE_DEPTH=1
CONFIG_MMC_DW=y
CONFIG_MMC_DW_ROCKCHIP=y
CONFIG_MMC_CQHCI=y
CONFIG_MMC_HSQ=y
CONFIG_RTC_CLASS=y
CONFIG_RTC_DRV_RK808=y
CONFIG_DMADEVICES=y
CONFIG_ROCKCHIP_DMA=y
CONFIG_DMABUF_HEAPS=y
CONFIG_DMABUF_HEAPS_SYSTEM=y
CONFIG_DMABUF_HEAPS_ROCKCHIP=y
CONFIG_DMABUF_HEAPS_ROCKCHIP_CMA_HEAP=y
CONFIG_DMABUF_RK_HEAPS_DEBUG=y
# CONFIG_VIRTIO_MENU is not set
# CONFIG_VHOST_MENU is not set
CONFIG_STAGING=y
# CONFIG_SURFACE_PLATFORMS is not set
CONFIG_COMMON_CLK_RK808=y
CONFIG_COMMON_CLK_SCMI=y
CONFIG_ROCKCHIP_CLK_OUT=y
# CONFIG_ROCKCHIP_CLK_PVTM is not set
CONFIG_ROCKCHIP_CLK_PVTPLL=y
# CONFIG_ARM_ARCH_TIMER_EVTSTREAM is not set
# CONFIG_FSL_ERRATUM_A008585 is not set
# CONFIG_HISILICON_ERRATUM_161010101 is not set
# CONFIG_ARM64_ERRATUM_858921 is not set
CONFIG_ROCKCHIP_IOMMU=y
CONFIG_CPU_RV1126B=y
CONFIG_ROCKCHIP_CPUINFO=y
CONFIG_ROCKCHIP_GRF=y
CONFIG_ROCKCHIP_OPP=y
CONFIG_ROCKCHIP_PERFORMANCE=y
CONFIG_ROCKCHIP_PM_DOMAINS=y
CONFIG_ROCKCHIP_SUSPEND_MODE=y
CONFIG_ROCKCHIP_SYSTEM_MONITOR=y
CONFIG_ROCKCHIP_VENDOR_STORAGE=y
CONFIG_ROCKCHIP_MMC_VENDOR_STORAGE=y
CONFIG_ROCKCHIP_VENDOR_STORAGE_UPDATE_LOADER=y
CONFIG_FIQ_DEBUGGER=y
CONFIG_FIQ_DEBUGGER_NO_SLEEP=y
CONFIG_FIQ_DEBUGGER_CONSOLE=y
CONFIG_FIQ_DEBUGGER_CONSOLE_DEFAULT_ENABLE=y
CONFIG_RK_CONSOLE_THREAD=y
CONFIG_ROCKCHIP_DEBUG=y
CONFIG_ROCKCHIP_MINI_KERNEL=y
CONFIG_PM_DEVFREQ=y
CONFIG_DEVFREQ_GOV_SIMPLE_ONDEMAND=y
CONFIG_DEVFREQ_GOV_USERSPACE=y
CONFIG_ARM_ROCKCHIP_BUS_DEVFREQ=y
CONFIG_ARM_ROCKCHIP_DMC_DEVFREQ=y
CONFIG_EXTCON=y
CONFIG_MEMORY=y
CONFIG_ROCKCHIP_DSMC=y
CONFIG_IIO=y
CONFIG_ROCKCHIP_SARADC=y
CONFIG_PWM=y
CONFIG_PWM_ROCKCHIP=y
CONFIG_PHY_ROCKCHIP_CSI2_DPHY=y
CONFIG_PHY_ROCKCHIP_INNO_USB2=y
CONFIG_PHY_ROCKCHIP_INNO_DSIDPHY=y
CONFIG_PHY_ROCKCHIP_NANENG_COMBO_PHY=y
CONFIG_NVMEM_ROCKCHIP_OTP=y
CONFIG_RK_HEADSET=y
CONFIG_ROCKCHIP_RKNPU=y
CONFIG_EXT4_FS=y
# CONFIG_DNOTIFY is not set
CONFIG_AUTOFS4_FS=y
CONFIG_VFAT_FS=y
CONFIG_TMPFS=y
CONFIG_PSTORE=y
CONFIG_PSTORE_CONSOLE=y
CONFIG_PSTORE_RAM=y
# CONFIG_NETWORK_FILESYSTEMS is not set
CONFIG_CRYPTO_DEV_ROCKCHIP=y
CONFIG_CRYPTO_DEV_ROCKCHIP_DEV=y
CONFIG_DMA_CMA=y
CONFIG_PRINTK_TIME=y
CONFIG_PRINTK_TIME_FROM_ARM_ARCH_TIMER=y
# CONFIG_DEBUG_MISC is not set
CONFIG_DEBUG_INFO_DWARF_TOOLCHAIN_DEFAULT=y
CONFIG_MAGIC_SYSRQ=y
# CONFIG_MAGIC_SYSRQ_SERIAL is not set
CONFIG_DEBUG_FS=y
# CONFIG_SLUB_DEBUG is not set
CONFIG_PANIC_ON_OOPS=y
CONFIG_BOOTPARAM_SOFTLOCKUP_PANIC=y
CONFIG_HARDLOCKUP_DETECTOR=y
CONFIG_BOOTPARAM_HARDLOCKUP_PANIC=y
# CONFIG_DETECT_HUNG_TASK is not set
# CONFIG_SCHED_DEBUG is not set
CONFIG_RCU_CPU_STALL_TIMEOUT=60
CONFIG_BOOTPARAM_RCU_STALL_PANIC=y
# CONFIG_RCU_TRACE is not set
# CONFIG_FTRACE is not set
# CONFIG_STRICT_DEVMEM is not set
# CONFIG_RUNTIME_TESTING_MENU is not set
