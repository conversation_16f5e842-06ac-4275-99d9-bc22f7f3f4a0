<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="rk" fetch="../../" />
  <default remote="rk" sync-j="4"/>

  <!-- Based file name： RV1126B_LINUX6.1_SDK_ALPHA_V0.0.1_20250326 -->
  <include name="include/rv1126b_doc-a0.xml"/>
  <include name="common/linux6.1/linux6.1-rka0.xml"/>
  <include name="common/yocto/yocto-scarthgap-alpha-a0.xml"/>
  <include name="common/debian/debian12-rka0.xml"/>
  <include name="common/ipc/rkipc-a0.xml"/>

  <!-- device/rockchip for rv1126b -->
  <project name="linux/device/rockchip" path="device/rockchip" revision="9b11f19c238ce31f159723357b1773a6c7c1ae32" upstream="rv1126b" dest-branch="rv1126b" clone-depth="1">
    <linkfile src="common/scripts/build.sh" dest="build.sh"/>
    <linkfile src="common/Makefile" dest="Makefile"/>
    <linkfile src="common/scripts/rkflash.sh" dest="rkflash.sh"/>
  </project>

  <project name="linux/external/camera_engine_rkaiq" path="external/camera_engine_rkaiq" revision="833f7ea585763dd2fccae08356f8e995466f4b45" upstream="rv1106" dest-branch="rv1106" clone-depth="1"/>

</manifest>
