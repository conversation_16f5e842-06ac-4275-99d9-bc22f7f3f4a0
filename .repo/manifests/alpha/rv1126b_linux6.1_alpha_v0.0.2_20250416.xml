<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="rk" fetch="../../" />
  <default remote="rk" sync-j="4"/>

  <!-- Based file name： RV1126B_LINUX6.1_SDK_ALPHA_V0.0.2_20250416 -->
  <include name="include/rv1126b_doc-a1.xml"/>
  <include name="common/linux6.1/linux6.1-rka1.xml"/>
  <include name="common/yocto/yocto-scarthgap-alpha-a1.xml"/>
  <include name="common/debian/debian12-rka1.xml"/>
  <include name="common/ipc/rkipc-a1.xml"/>

  <!-- device/rockchip for rv1126b -->
  <project name="linux/device/rockchip" path="device/rockchip" revision="1b59b7fdf49cfc731ba8dc8a29e4fee076e0b65b" upstream="rv1126b" dest-branch="rv1126b" clone-depth="1">
    <linkfile src="common/scripts/build.sh" dest="build.sh"/>
    <linkfile src="common/Makefile" dest="Makefile"/>
    <linkfile src="common/scripts/rkflash.sh" dest="rkflash.sh"/>
  </project>

  <project name="linux/external/camera_engine_rkaiq" path="external/camera_engine_rkaiq" revision="1ee735c88089a4fbf467b2234f9e19356adcfc39" upstream="rv1106" dest-branch="rv1106" clone-depth="1"/>

</manifest>
