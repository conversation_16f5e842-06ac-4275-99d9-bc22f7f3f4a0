<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="android/rk/platform/system/rk_tee_user" path="external/security/rk_tee_user" revision="24d4e439f6c41ec19e9f44b48960e7057571705a" upstream="develop-next" dest-branch="develop-next" clone-depth="1"/>
  <project name="android/rk/u-boot" path="u-boot" revision="2248b0d2e75e4fc8b89ed71bbadbc72ecb2c29d4" upstream="next-dev" dest-branch="next-dev"/>
  <project name="linux/app/lvgl_demo" path="app/lvgl_demo" revision="f6876395374d97d51a807e03f085e0158b43dd6b" upstream="develop" dest-branch="develop"/>
  <project name="linux/buildroot" path="buildroot" revision="ee12bf497cad4bf1283b154c5aa7c636342ee23b" upstream="rockchip/2024.02" dest-branch="rockchip/2024.02">
    <linkfile src="build/envsetup.sh" dest="envsetup.sh"/>
  </project>
  <project name="linux/external/alsa-config" path="external/alsa-config" revision="5529257039c41955724c1069796f5cb7b2015c77" upstream="master" dest-branch="master"/>
  <project name="linux/external/common_algorithm" path="external/common_algorithm" revision="10914d14c8eba2471dac62a9cde068fc0a2abdbc" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rkscript" path="external/rkscript" revision="a8c3b53758a08253a332a9b1de4ec3e75d6954e5" upstream="master" dest-branch="master"/>
  <project name="linux/external/rktoolkit" path="external/rktoolkit" revision="bc1d85d6d2a39c67ed2d619f6faf1a996d542722" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkupdate" path="external/rkupdate" revision="e9cd4fe07733f807057fd7a6bd5e8d42a59624a7" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkwifibt" path="external/rkwifibt" revision="c0136c0df1a32a163ed49de862b8b6c7a41f89d5" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rkwifibt-app" path="external/rkwifibt-app" revision="5ca9c78c9c065d4bda11b9cca3cc5cf9d51e8b78" upstream="master" dest-branch="master"/>
  <project name="linux/gstreamer-rockchip" path="external/gstreamer-rockchip" revision="98c855b7fd7c61b10521a4a54809e12a067d22fa" upstream="master" dest-branch="master"/>
  <project name="linux/linux-rga" path="external/linux-rga" revision="d61ba10664968528696a3e3f56ee1533721b48a3" upstream="multi_rga_im2d_api" dest-branch="multi_rga_im2d_api"/>
  <project name="linux/recovery" path="external/recovery" revision="7edb5d5a73d8e9dacbb52ba0b547175c222f4f8a" upstream="develop" dest-branch="develop"/>
  <project name="linux/rockchip-test" path="external/rockchip-test" revision="18352108eeb0664ff8337af8da53e88d366bd532" upstream="master" dest-branch="master"/>
  <project name="linux/security/bin" path="external/security/bin" revision="f085f4acd9de757a9b11ba0b799cd730764a552c" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/tools" path="tools" revision="f69c27324d9c37822fb345d8104ab3c4a6d6bb33" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/kernel-stable" path="kernel-6.1" revision="625ce41d30508b2671175f1b1c59a1c3b0a7bb5b" upstream="release-6.1" dest-branch="release-6.1">
    <linkfile src="." dest="kernel"/>
  </project>
  <project name="rk/librkcrypto" path="external/security/librkcrypto" revision="aefdff3f45de8d94075fc9f2734dde99bfeb750e" upstream="master" dest-branch="master"/>
  <project name="rk/mpp" path="external/mpp" revision="4999c7762f5f4052ebe053f92d0a6d217f4f95db" upstream="develop" dest-branch="develop"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" revision="adbb295a970c4b39dc487c95226fe84d2c460072" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/rkbin" path="rkbin" revision="c66402be0aa57aad0daac84279ffdff7dd14b433" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/rknn-llm" path="external/rknn-llm" revision="11d5978cf683b588bac55874567b1c069c820a74" upstream="main" dest-branch="main" clone-depth="1"/>
  <project name="rk/rknn-toolkit2" path="external/rknn-toolkit2" revision="cef93faedd3375c8df9f47ed959f496ab15d29b0" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/rknpu2" path="external/rknpu2" revision="e50bab83ae3c1a2c93be478a8292f0b04d8ddfc3" upstream="master" dest-branch="master" clone-depth="1"/>
</manifest>
