<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="android/rk/platform/system/rk_tee_user" path="external/security/rk_tee_user" revision="e01afe5c877d9a86e9f83aae846f5cf04365989d" upstream="develop-next" dest-branch="develop-next" clone-depth="1"/>
  <project name="android/rk/u-boot" path="u-boot" revision="4d9e803d4933a9fe325528bf60b2dc7cd181de34" upstream="next-dev" dest-branch="next-dev"/>
  <project name="linux/app/lvgl_demo" path="app/lvgl_demo" revision="f6876395374d97d51a807e03f085e0158b43dd6b" upstream="develop" dest-branch="develop"/>
  <project name="linux/buildroot" path="buildroot" revision="ce27c04cddea09fa5edd663be2d1224680517c09" upstream="rockchip/2024.02" dest-branch="rockchip/2024.02">
    <linkfile src="build/envsetup.sh" dest="envsetup.sh"/>
  </project>
  <project name="linux/external/alsa-config" path="external/alsa-config" revision="5529257039c41955724c1069796f5cb7b2015c77" upstream="master" dest-branch="master"/>
  <project name="linux/external/common_algorithm" path="external/common_algorithm" revision="10914d14c8eba2471dac62a9cde068fc0a2abdbc" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rkscript" path="external/rkscript" revision="d56d66a25560d6f7d1968531e71712fd2b802e18" upstream="master" dest-branch="master"/>
  <project name="linux/external/rktoolkit" path="external/rktoolkit" revision="bc1d85d6d2a39c67ed2d619f6faf1a996d542722" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkupdate" path="external/rkupdate" revision="e9cd4fe07733f807057fd7a6bd5e8d42a59624a7" upstream="master" dest-branch="master"/>
  <project name="linux/external/rkwifibt" path="external/rkwifibt" revision="75e559d4e91b029f555fa9d8a10b6edb30e77f22" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/rkwifibt-app" path="external/rkwifibt-app" revision="0673a0b73af5b2027ea7e3631fcd610f45941cee" upstream="master" dest-branch="master"/>
  <project name="linux/gstreamer-rockchip" path="external/gstreamer-rockchip" revision="98c855b7fd7c61b10521a4a54809e12a067d22fa" upstream="master" dest-branch="master"/>
  <project name="linux/linux-rga" path="external/linux-rga" revision="d61ba10664968528696a3e3f56ee1533721b48a3" upstream="multi_rga_im2d_api" dest-branch="multi_rga_im2d_api"/>
  <project name="linux/recovery" path="external/recovery" revision="7edb5d5a73d8e9dacbb52ba0b547175c222f4f8a" upstream="develop" dest-branch="develop"/>
  <project name="linux/rockchip-test" path="external/rockchip-test" revision="8cef23db291447d3892208fdcd32e90a252ab533" upstream="master" dest-branch="master"/>
  <project name="linux/security/bin" path="external/security/bin" revision="f085f4acd9de757a9b11ba0b799cd730764a552c" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/tools" path="tools" revision="f69c27324d9c37822fb345d8104ab3c4a6d6bb33" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/kernel-stable" path="kernel-6.1" revision="a7874a7ba26d3e917fad30eef61ea71081728d20" upstream="release-6.1" dest-branch="release-6.1">
    <linkfile src="." dest="kernel"/>
  </project>
  <project name="rk/librkcrypto" path="external/security/librkcrypto" revision="5a42a784f52f1cabf6c12e3bfd1818dcf7dab592" upstream="master" dest-branch="master"/>
  <project name="rk/mpp" path="external/mpp" revision="5d7a5cb7c5708bd9a5aa76b9ec769c44c51c61d2" upstream="develop" dest-branch="develop"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu" revision="adbb295a970c4b39dc487c95226fe84d2c460072" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/prebuilts/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" path="prebuilts/gcc/linux-x86/arm/gcc-arm-10.3-2021.07-x86_64-arm-none-linux-gnueabihf" revision="2e89382ca3f8c34086272509ae4d7a5c340ffafc" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="rk/rkbin" path="rkbin" revision="06558f3d55bc1c55610831481d05bc7e40c0f3f1" upstream="for-next" dest-branch="for-next" clone-depth="1"/>
</manifest>
