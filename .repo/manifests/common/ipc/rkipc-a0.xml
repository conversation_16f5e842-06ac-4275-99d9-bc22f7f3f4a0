<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <project name="linux/app/rkipc" path="app/rkipc" revision="222f0dcc2ccc085c9572b4d2078c158fbe269dd4" upstream="master" dest-branch="master"/>
  <project name="linux/app/web-new/ipcweb-backend" path="app/ipcweb-backend" revision="42d23ee928d8e3c93c1c98968b2ade8657d0ec61" upstream="master" dest-branch="master"/>
  <project name="linux/ipc/media/iva" path="external/iva" revision="5ba3acd84f0163c08f19c3c6cc1a9b832bb47b14" upstream="rockiva_v2" dest-branch="rockiva_v2" clone-depth="1"/>
  <project name="linux/ipc/media/avs" path="external/avs" revision="ba1c01ac5a5eb75644b6320bf05cc637494d773f" upstream="master" dest-branch="master" clone-depth="1"/>
  <project name="linux/external/minilogger" path="external/minilogger" revision="5f0fa4698d5305d8653c2352bfcf1d9aa57064a4" upstream="master" dest-branch="master"/>
  <project name="linux/rkfsmk" path="external/rkfsmk" revision="31ef4a6a53b461d8e145020d89480c42f375f2b3" upstream="master" dest-branch="master"/>
  <project name="linux/rockit" path="external/rockit" revision="dd36a97fbb76910caee3adb20c5a83497eaf6018" upstream="rockit-c" dest-branch="rockit-c"/>
  <project name="linux/ipc/media/samples" path="external/samples" revision="f8725acd62c60c02762895da436004969b7f1323" upstream="rv1106" dest-branch="rv1106"/>
  <project name="linux/ipc/sysdrv/drv_ko" path="external/ipc_drv_ko" revision="4c9467a93c30a445831b06291b8d3bc936e82329" upstream="rv1126b-buildroot" dest-branch="rv1126b-buildroot" clone-depth="1"/>

</manifest>
