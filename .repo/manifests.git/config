[core]
	repositoryformatversion = 0
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = https://gerrit.rock-chips.com:8443/linux/rockchip/platform/manifests
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "default"]
	remote = origin
	merge = refs/heads/rv1126b
[repo "syncstate.main"]
	synctime = 2025-04-18T03:44:02.390040Z
	version = 1
[repo "syncstate.sys"]
	argv = ['/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release/.repo/repo/main.py', '--repo-dir=/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release/.repo', '--wrapper-version=2.17', '--wrapper-path=/home/<USER>/share/disk2/danny/sdk/1126B/rv1126b_linux6.1_release/.repo/repo/repo', '--', 'sync', '-c', '-j4']
[repo "syncstate.superproject"]
	superproject = false
	haslocalmanifests = true
	hassuperprojecttag = false
[repo "syncstate.options"]
	jobs = 4
	localonly = true
	mpupdate = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
	forcesync = true
	currentbranchonly = true
[repo "syncstate.remote.origin"]
	url = https://gerrit.rock-chips.com:8443/linux/rockchip/platform/manifests
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/rv1126b
